name: aviator_predictor_flutter
description: "Aviator Predictor Pro - Advanced crash prediction with AI algorithms"
publish_to: 'none'

version: 2.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Navigation
  cupertino_icons: ^1.0.6
  flutter_riverpod: ^2.4.9
  go_router: ^12.1.3
  
  # Charts & Visualization
  fl_chart: ^0.65.0
  syncfusion_flutter_charts: ^23.2.7
  
  # Data & Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  
  # HTTP & API
  dio: ^5.4.0
  connectivity_plus: ^5.0.2
  
  # Math & ML
  ml_algo: ^16.14.0
  ml_dataframe: ^1.4.1
  
  # Utils
  intl: ^0.19.0
  uuid: ^4.2.1
  logger: ^2.0.2+1
  path_provider: ^2.1.1
  
  # Animations
  lottie: ^2.7.0
  animated_text_kit: ^4.2.2
  
  # Platform specific
  window_manager: ^0.3.7
  desktop_window: ^0.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/animations/
    - assets/sounds/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
