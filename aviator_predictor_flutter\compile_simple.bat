@echo off
echo ========================================
echo   AVIATOR PREDICTOR PRO - COMPILATION
echo ========================================
echo.

echo [1/4] Verification de Flutter...
flutter --version
if %errorlevel% neq 0 (
    echo ERREUR: Flutter non installe
    pause
    exit /b 1
)

echo [2/4] Nettoyage et preparation...
flutter clean
flutter pub get

echo [3/4] Compilation Web...
flutter build web --release
if %errorlevel% neq 0 (
    echo ERREUR: Echec compilation Web
    pause
    exit /b 1
)

echo [4/4] Compilation Windows...
flutter build windows --release
if %errorlevel% neq 0 (
    echo ERREUR: Echec compilation Windows
    pause
    exit /b 1
)

echo.
echo ========================================
echo           COMPILATION TERMINEE!
echo ========================================
echo.
echo Applications disponibles:
echo - Windows: build\windows\x64\runner\Release\aviator_predictor_flutter.exe
echo - Web: build\web\index.html
echo.
echo Pour tester:
echo - Windows: Double-cliquer sur l'executable
echo - Web: Ouvrir index.html dans un navigateur
echo.
pause
