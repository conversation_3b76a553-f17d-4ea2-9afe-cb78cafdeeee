import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_theme.dart';
import '../providers/game_provider.dart';
import '../widgets/algorithm_card.dart';
import '../widgets/prediction_comparison_chart.dart';

class PredictionLabScreen extends ConsumerStatefulWidget {
  const PredictionLabScreen({super.key});

  @override
  ConsumerState<PredictionLabScreen> createState() => _PredictionLabScreenState();
}

class _PredictionLabScreenState extends ConsumerState<PredictionLabScreen> 
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedAlgorithm = 'ensemble';
  bool _isRunningComparison = false;

  final List<Map<String, dynamic>> _algorithms = [
    {
      'id': 'lstm',
      'name': 'LSTM Neural Network',
      'description': 'Réseau de neurones à mémoire long-terme pour capturer les séquences complexes',
      'icon': Icons.psychology,
      'color': AppTheme.primaryColor,
      'complexity': 'Élevée',
      'accuracy': '85%',
    },
    {
      'id': 'ensemble',
      'name': 'Ensemble Learning',
      'description': 'Combinaison de multiples algorithmes pour une prédiction robuste',
      'icon': Icons.group_work,
      'color': AppTheme.secondaryColor,
      'complexity': 'Très Élevée',
      'accuracy': '88%',
    },
    {
      'id': 'reinforcement',
      'name': 'Reinforcement Learning',
      'description': 'Apprentissage par renforcement adaptatif basé sur les récompenses',
      'icon': Icons.smart_toy,
      'color': AppTheme.successColor,
      'complexity': 'Élevée',
      'accuracy': '82%',
    },
    {
      'id': 'genetic',
      'name': 'Genetic Algorithm',
      'description': 'Algorithme évolutionnaire optimisant les stratégies de prédiction',
      'icon': Icons.dna,
      'color': AppTheme.warningColor,
      'complexity': 'Modérée',
      'accuracy': '79%',
    },
    {
      'id': 'quantum',
      'name': 'Quantum-Inspired',
      'description': 'Simulation quantique pour explorer les probabilités multiples',
      'icon': Icons.scatter_plot,
      'color': AppTheme.errorColor,
      'complexity': 'Expérimentale',
      'accuracy': '76%',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.science,
                color: Colors.black,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text('Laboratoire de Prédiction IA'),
          ],
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.psychology), text: 'Algorithmes'),
            Tab(icon: Icon(Icons.compare_arrows), text: 'Comparaison'),
            Tab(icon: Icon(Icons.tune), text: 'Optimisation'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAlgorithmsTab(gameState),
          _buildComparisonTab(gameState),
          _buildOptimizationTab(gameState),
        ],
      ),
    );
  }

  Widget _buildAlgorithmsTab(GameState gameState) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.info_outline, color: AppTheme.primaryColor),
                      const SizedBox(width: 8),
                      Text(
                        'Algorithmes d\'IA Avancés',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Explorez différents algorithmes d\'intelligence artificielle pour la prédiction des multiplicateurs Aviator. Chaque algorithme utilise une approche unique pour analyser les patterns.',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: _algorithms.length,
              itemBuilder: (context, index) {
                final algorithm = _algorithms[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12.0),
                  child: AlgorithmCard(
                    algorithm: algorithm,
                    isSelected: _selectedAlgorithm == algorithm['id'],
                    onTap: () => _selectAlgorithm(algorithm['id']),
                    onPredict: () => _runPrediction(algorithm['id']),
                    isLoading: gameState.isLoading,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComparisonTab(GameState gameState) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.compare_arrows, color: AppTheme.secondaryColor),
                      const SizedBox(width: 8),
                      Text(
                        'Comparaison des Algorithmes',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Comparez les performances de tous les algorithmes sur vos données historiques.',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _isRunningComparison ? null : _runComparison,
                          icon: _isRunningComparison 
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Icon(Icons.play_arrow),
                          label: Text(_isRunningComparison ? 'Analyse en cours...' : 'Lancer la Comparaison'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      ElevatedButton.icon(
                        onPressed: () => _exportResults(),
                        icon: const Icon(Icons.download),
                        label: const Text('Exporter'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.secondaryColor,
                          foregroundColor: Colors.black,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: PredictionComparisonChart(
                  results: gameState.results,
                  predictions: gameState.predictions,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptimizationTab(GameState gameState) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.tune, color: AppTheme.warningColor),
                      const SizedBox(width: 8),
                      Text(
                        'Optimisation des Paramètres',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Ajustez les paramètres des algorithmes pour optimiser les performances.',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView(
              children: [
                _buildParameterCard(
                  'Fenêtre d\'Analyse',
                  'Nombre de parties à analyser',
                  Icons.window,
                  15,
                  5,
                  50,
                ),
                const SizedBox(height: 12),
                _buildParameterCard(
                  'Seuil de Confiance',
                  'Niveau minimum de confiance requis',
                  Icons.security,
                  70,
                  50,
                  95,
                ),
                const SizedBox(height: 12),
                _buildParameterCard(
                  'Sensibilité aux Crashs',
                  'Importance accordée aux prédictions de crash',
                  Icons.warning,
                  80,
                  50,
                  100,
                ),
                const SizedBox(height: 12),
                _buildParameterCard(
                  'Période d\'Apprentissage',
                  'Nombre de parties pour l\'entraînement',
                  Icons.school,
                  100,
                  50,
                  500,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildParameterCard(String title, String description, IconData icon, 
                           double value, double min, double max) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      Text(
                        description,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    value.toStringAsFixed(0),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Slider(
              value: value,
              min: min,
              max: max,
              divisions: ((max - min) / 5).round(),
              activeColor: AppTheme.primaryColor,
              onChanged: (newValue) {
                // TODO: Update parameter value
              },
            ),
          ],
        ),
      ),
    );
  }

  void _selectAlgorithm(String algorithmId) {
    setState(() {
      _selectedAlgorithm = algorithmId;
    });
  }

  void _runPrediction(String algorithmId) {
    ref.read(gameProvider.notifier).makeAdvancedPrediction(algorithmId);
  }

  void _runComparison() async {
    setState(() {
      _isRunningComparison = true;
    });

    // Simulate running all algorithms
    for (final algorithm in _algorithms) {
      await Future.delayed(const Duration(milliseconds: 500));
      ref.read(gameProvider.notifier).makeAdvancedPrediction(algorithm['id']);
    }

    setState(() {
      _isRunningComparison = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Comparaison terminée! Consultez les résultats ci-dessous.'),
          backgroundColor: AppTheme.successColor,
        ),
      );
    }
  }

  void _exportResults() {
    // TODO: Implement export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Fonctionnalité d\'export en cours de développement'),
        backgroundColor: AppTheme.warningColor,
      ),
    );
  }
}
