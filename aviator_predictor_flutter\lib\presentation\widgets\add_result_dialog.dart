import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_theme.dart';
import '../../data/models/game_result.dart';
import '../providers/game_provider.dart';

class AddResultDialog extends ConsumerStatefulWidget {
  const AddResultDialog({super.key});

  @override
  ConsumerState<AddResultDialog> createState() => _AddResultDialogState();
}

class _AddResultDialogState extends ConsumerState<AddResultDialog> 
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _multiplierController = TextEditingController();
  final _betAmountController = TextEditingController();
  final _crashTimeController = TextEditingController();
  
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  
  String _selectedStrategy = 'manual';
  bool _isCrash = false;
  bool _isAutoDetectCrash = true;
  double? _volatility;
  
  final List<Map<String, dynamic>> _strategies = [
    {'id': 'manual', 'name': '<PERSON>', 'icon': Icons.touch_app},
    {'id': 'conservative', 'name': 'Conservateur', 'icon': Icons.security},
    {'id': 'aggressive', 'name': 'Agressif', 'icon': Icons.trending_up},
    {'id': 'balanced', 'name': 'Équilibré', 'icon': Icons.balance},
  ];

  @override
  void initState() {
    super.initState();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    _slideController.forward();
    
    // Écouter les changements du multiplicateur pour auto-détecter les crashs
    _multiplierController.addListener(_onMultiplierChanged);
  }

  @override
  void dispose() {
    _slideController.dispose();
    _multiplierController.dispose();
    _betAmountController.dispose();
    _crashTimeController.dispose();
    super.dispose();
  }

  void _onMultiplierChanged() {
    if (_isAutoDetectCrash) {
      final text = _multiplierController.text;
      if (text.isNotEmpty) {
        final multiplier = double.tryParse(text);
        if (multiplier != null) {
          setState(() {
            _isCrash = multiplier <= 1.1;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 500),
          decoration: BoxDecoration(
            color: Theme.of(context).dialogBackgroundColor,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              _buildForm(),
              _buildActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.add_circle,
              color: Colors.black,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Ajouter un Résultat',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Enregistrer le résultat de la partie',
                  style: TextStyle(
                    color: Colors.black.withOpacity(0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.black),
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildMultiplierField(),
            const SizedBox(height: 16),
            _buildCrashTimeField(),
            const SizedBox(height: 16),
            _buildBetAmountField(),
            const SizedBox(height: 16),
            _buildStrategySelector(),
            const SizedBox(height: 16),
            _buildCrashDetection(),
            const SizedBox(height: 16),
            _buildVolatilitySlider(),
          ],
        ),
      ),
    );
  }

  Widget _buildMultiplierField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Multiplicateur *',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _multiplierController,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
          ],
          decoration: InputDecoration(
            hintText: 'Ex: 2.45',
            prefixIcon: const Icon(Icons.close),
            suffixText: 'x',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Veuillez entrer un multiplicateur';
            }
            final multiplier = double.tryParse(value);
            if (multiplier == null || multiplier <= 0) {
              return 'Veuillez entrer un multiplicateur valide';
            }
            if (multiplier > 1000) {
              return 'Multiplicateur trop élevé (max: 1000x)';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildCrashTimeField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Temps avant crash (secondes)',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _crashTimeController,
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
          ],
          decoration: InputDecoration(
            hintText: 'Ex: 15',
            prefixIcon: const Icon(Icons.timer),
            suffixText: 's',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
          ),
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              final time = int.tryParse(value);
              if (time == null || time < 0 || time > 300) {
                return 'Temps invalide (0-300 secondes)';
              }
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildBetAmountField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Montant de mise (optionnel)',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _betAmountController,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
          ],
          decoration: InputDecoration(
            hintText: 'Ex: 10.00',
            prefixIcon: const Icon(Icons.attach_money),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
          ),
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              final amount = double.tryParse(value);
              if (amount == null || amount < 0) {
                return 'Montant invalide';
              }
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildStrategySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Stratégie utilisée',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _strategies.map((strategy) {
            final isSelected = _selectedStrategy == strategy['id'];
            return FilterChip(
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _selectedStrategy = strategy['id'];
                  });
                }
              },
              avatar: Icon(
                strategy['icon'] as IconData,
                size: 18,
                color: isSelected ? Colors.black : null,
              ),
              label: Text(strategy['name']),
              selectedColor: AppTheme.secondaryColor,
              checkmarkColor: Colors.black,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildCrashDetection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _isCrash ? AppTheme.errorColor.withOpacity(0.1) : AppTheme.successColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isCrash ? AppTheme.errorColor : AppTheme.successColor,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                _isCrash ? Icons.warning : Icons.check_circle,
                color: _isCrash ? AppTheme.errorColor : AppTheme.successColor,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _isCrash ? 'Crash détecté' : 'Pas de crash',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _isCrash ? AppTheme.errorColor : AppTheme.successColor,
                  ),
                ),
              ),
              Switch(
                value: _isAutoDetectCrash,
                onChanged: (value) {
                  setState(() {
                    _isAutoDetectCrash = value;
                  });
                },
                activeColor: AppTheme.primaryColor,
              ),
            ],
          ),
          if (!_isAutoDetectCrash) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<bool>(
                    title: const Text('Pas de crash'),
                    value: false,
                    groupValue: _isCrash,
                    onChanged: (value) {
                      setState(() {
                        _isCrash = value!;
                      });
                    },
                    dense: true,
                  ),
                ),
                Expanded(
                  child: RadioListTile<bool>(
                    title: const Text('Crash'),
                    value: true,
                    groupValue: _isCrash,
                    onChanged: (value) {
                      setState(() {
                        _isCrash = value!;
                      });
                    },
                    dense: true,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildVolatilitySlider() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Volatilité du marché: ${(_volatility ?? 0.5).toStringAsFixed(2)}',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Slider(
          value: _volatility ?? 0.5,
          min: 0.0,
          max: 1.0,
          divisions: 20,
          activeColor: AppTheme.primaryColor,
          onChanged: (value) {
            setState(() {
              _volatility = value;
            });
          },
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Faible',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              'Élevée',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActions() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Annuler'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: _saveResult,
              icon: const Icon(Icons.save),
              label: const Text('Sauvegarder'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _saveResult() {
    if (_formKey.currentState!.validate()) {
      final multiplier = double.parse(_multiplierController.text);
      final betAmount = _betAmountController.text.isNotEmpty 
          ? double.parse(_betAmountController.text) 
          : null;
      final crashTime = _crashTimeController.text.isNotEmpty 
          ? int.parse(_crashTimeController.text) 
          : null;

      final result = GameResult.create(
        multiplier: multiplier,
        betAmount: betAmount,
        strategy: _selectedStrategy,
        crashTimeSeconds: crashTime,
        volatilityAtTime: _volatility,
        isAutoSaved: true,
      );

      ref.read(gameProvider.notifier).addResult(result);

      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '✅ Résultat ajouté: ${multiplier}x ${_isCrash ? "(Crash)" : ""}',
          ),
          backgroundColor: AppTheme.successColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
  }
}
