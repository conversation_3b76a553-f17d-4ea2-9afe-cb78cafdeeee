import 'dart:math';
import '../../data/models/game_result.dart';

class MultiplierProbabilityService {
  // Ranges de multiplicateurs prédéfinis
  static const List<MultiplierRange> _ranges = [
    MultiplierRange(1.0, 1.5, 'Tr<PERSON> Faible', 'SAFE'),
    MultiplierRange(1.5, 2.0, '<PERSON><PERSON>ble', 'LOW'),
    MultiplierRange(2.0, 3.0, '<PERSON><PERSON><PERSON><PERSON>', 'MEDIUM'),
    MultiplierRange(3.0, 5.0, 'Élevé', 'HIGH'),
    MultiplierRange(5.0, 10.0, 'Très Élevé', 'VERY_HIGH'),
    MultiplierRange(10.0, 50.0, 'Extrême', 'EXTREME'),
    MultiplierRange(50.0, 100.0, 'Légendaire', 'LEGENDARY'),
    MultiplierRange(100.0, double.infinity, 'Mythique', 'MYTHIC'),
  ];

  // Multiplicateurs spécifiques populaires
  static const List<double> _popularMultipliers = [
    1.2, 1.5, 2.0, 2.5, 3.0, 4.0, 5.0, 7.5, 10.0, 15.0, 20.0, 50.0, 100.0
  ];

  // Analyser les probabilités de tous les multiplicateurs
  static MultiplierAnalysis analyzeMultiplierProbabilities(List<GameResult> history) {
    if (history.isEmpty) {
      return MultiplierAnalysis.empty();
    }

    final multipliers = history.map((r) => r.multiplier).toList();
    final totalGames = history.length;

    // Calculer les probabilités par range
    final rangeProbabilities = <MultiplierRange, double>{};
    for (final range in _ranges) {
      final count = multipliers.where((m) => m >= range.min && m < range.max).length;
      rangeProbabilities[range] = (count / totalGames) * 100;
    }

    // Calculer les probabilités pour les multiplicateurs populaires
    final popularProbabilities = <double, double>{};
    for (final target in _popularMultipliers) {
      popularProbabilities[target] = calculateSpecificMultiplierProbability(target, history);
    }

    // Statistiques générales
    final stats = _calculateGeneralStats(multipliers);

    // Prédictions futures
    final predictions = _generateFuturePredictions(history);

    // Patterns temporels
    final patterns = _analyzeTemporalPatterns(history);

    return MultiplierAnalysis(
      totalGames: totalGames,
      rangeProbabilities: rangeProbabilities,
      popularProbabilities: popularProbabilities,
      generalStats: stats,
      futurePredictions: predictions,
      temporalPatterns: patterns,
      lastUpdated: DateTime.now(),
    );
  }

  // Calculer la probabilité d'un multiplicateur spécifique
  static double calculateSpecificMultiplierProbability(double targetMultiplier, List<GameResult> history) {
    if (history.isEmpty) return 0.0;

    // Tolérance basée sur la valeur du multiplicateur
    double tolerance;
    if (targetMultiplier < 2.0) {
      tolerance = 0.1;
    } else if (targetMultiplier < 5.0) {
      tolerance = 0.2;
    } else if (targetMultiplier < 10.0) {
      tolerance = 0.5;
    } else {
      tolerance = 1.0;
    }

    final matchingResults = history.where((result) => 
      (result.multiplier - targetMultiplier).abs() <= tolerance
    ).length;

    return (matchingResults / history.length) * 100.0;
  }

  // Prédire la probabilité d'atteindre un multiplicateur dans les N prochaines parties
  static double predictProbabilityInNextGames(double targetMultiplier, List<GameResult> history, int nextGames) {
    final baseProbability = calculateSpecificMultiplierProbability(targetMultiplier, history) / 100.0;
    
    // Probabilité qu'au moins une occurrence se produise dans les N prochaines parties
    final probabilityNone = pow(1 - baseProbability, nextGames);
    final probabilityAtLeastOne = 1 - probabilityNone;
    
    return probabilityAtLeastOne * 100.0;
  }

  // Analyser les séquences de multiplicateurs
  static SequenceAnalysis analyzeSequences(List<GameResult> history) {
    if (history.length < 3) {
      return SequenceAnalysis.empty();
    }

    final sequences = <String, int>{};
    final transitions = <String, Map<String, int>>{};

    // Analyser les séquences de 3 multiplicateurs
    for (int i = 0; i < history.length - 2; i++) {
      final seq = [
        _categorizeMultiplier(history[i].multiplier),
        _categorizeMultiplier(history[i + 1].multiplier),
        _categorizeMultiplier(history[i + 2].multiplier),
      ].join('-');
      
      sequences[seq] = (sequences[seq] ?? 0) + 1;
    }

    // Analyser les transitions
    for (int i = 0; i < history.length - 1; i++) {
      final from = _categorizeMultiplier(history[i].multiplier);
      final to = _categorizeMultiplier(history[i + 1].multiplier);
      
      transitions[from] ??= <String, int>{};
      transitions[from]![to] = (transitions[from]![to] ?? 0) + 1;
    }

    // Trouver les séquences les plus communes
    final sortedSequences = sequences.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return SequenceAnalysis(
      commonSequences: Map.fromEntries(sortedSequences.take(10)),
      transitions: transitions,
      totalSequences: sequences.length,
    );
  }

  // Prédire le prochain multiplicateur basé sur les patterns
  static NextMultiplierPrediction predictNextMultiplier(List<GameResult> history) {
    if (history.length < 5) {
      return NextMultiplierPrediction.default();
    }

    final recent = history.take(3).map((r) => r.multiplier).toList();
    final sequences = analyzeSequences(history);
    
    // Chercher des patterns similaires
    final currentPattern = recent.map(_categorizeMultiplier).join('-');
    
    // Analyser les transitions possibles
    final lastCategory = _categorizeMultiplier(recent.first);
    final possibleTransitions = sequences.transitions[lastCategory] ?? {};
    
    if (possibleTransitions.isEmpty) {
      return NextMultiplierPrediction.default();
    }

    // Calculer les probabilités de transition
    final totalTransitions = possibleTransitions.values.reduce((a, b) => a + b);
    final transitionProbabilities = <String, double>{};
    
    for (final entry in possibleTransitions.entries) {
      transitionProbabilities[entry.key] = (entry.value / totalTransitions) * 100;
    }

    // Prédire le multiplicateur le plus probable
    final mostLikelyCategory = transitionProbabilities.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
    
    final predictedRange = _getCategoryRange(mostLikelyCategory);
    final predictedMultiplier = (predictedRange.min + predictedRange.max) / 2;
    final confidence = transitionProbabilities[mostLikelyCategory]!;

    return NextMultiplierPrediction(
      predictedMultiplier: predictedMultiplier,
      confidence: confidence,
      category: mostLikelyCategory,
      range: predictedRange,
      basedOnPattern: currentPattern,
      alternatives: transitionProbabilities,
    );
  }

  // Calculer les statistiques générales
  static Map<String, dynamic> _calculateGeneralStats(List<double> multipliers) {
    if (multipliers.isEmpty) return {};

    multipliers.sort();
    
    final sum = multipliers.reduce((a, b) => a + b);
    final average = sum / multipliers.length;
    
    final median = multipliers.length % 2 == 0
        ? (multipliers[multipliers.length ~/ 2 - 1] + multipliers[multipliers.length ~/ 2]) / 2
        : multipliers[multipliers.length ~/ 2];
    
    final variance = multipliers
        .map((m) => pow(m - average, 2))
        .reduce((a, b) => a + b) / multipliers.length;
    
    final standardDeviation = sqrt(variance);
    
    return {
      'average': average,
      'median': median,
      'min': multipliers.first,
      'max': multipliers.last,
      'standardDeviation': standardDeviation,
      'variance': variance,
      'range': multipliers.last - multipliers.first,
    };
  }

  // Générer des prédictions futures
  static Map<String, dynamic> _generateFuturePredictions(List<GameResult> history) {
    final analysis = analyzeMultiplierProbabilities(history);
    
    return {
      'nextHighMultiplier': _predictNextHighMultiplier(history),
      'crashProbability': _calculateCrashProbability(history),
      'volatilityTrend': _calculateVolatilityTrend(history),
      'recommendedStrategy': _recommendStrategy(history),
    };
  }

  // Analyser les patterns temporels
  static Map<String, dynamic> _analyzeTemporalPatterns(List<GameResult> history) {
    if (history.length < 10) return {};

    // Analyser par heure de la journée
    final hourlyDistribution = <int, List<double>>{};
    for (final result in history) {
      final hour = result.timestamp.hour;
      hourlyDistribution[hour] ??= [];
      hourlyDistribution[hour]!.add(result.multiplier);
    }

    // Analyser par jour de la semaine
    final weeklyDistribution = <int, List<double>>{};
    for (final result in history) {
      final weekday = result.timestamp.weekday;
      weeklyDistribution[weekday] ??= [];
      weeklyDistribution[weekday]!.add(result.multiplier);
    }

    return {
      'hourlyPatterns': hourlyDistribution,
      'weeklyPatterns': weeklyDistribution,
      'bestHours': _findBestHours(hourlyDistribution),
      'bestDays': _findBestDays(weeklyDistribution),
    };
  }

  // Méthodes utilitaires
  static String _categorizeMultiplier(double multiplier) {
    for (final range in _ranges) {
      if (multiplier >= range.min && multiplier < range.max) {
        return range.riskLevel;
      }
    }
    return 'UNKNOWN';
  }

  static MultiplierRange _getCategoryRange(String category) {
    return _ranges.firstWhere(
      (range) => range.riskLevel == category,
      orElse: () => const MultiplierRange(1.0, 2.0, 'Default', 'LOW'),
    );
  }

  static double _predictNextHighMultiplier(List<GameResult> history) {
    final highMultipliers = history
        .where((r) => r.multiplier >= 5.0)
        .map((r) => r.multiplier)
        .toList();
    
    if (highMultipliers.isEmpty) return 5.0;
    
    return highMultipliers.reduce((a, b) => a + b) / highMultipliers.length;
  }

  static double _calculateCrashProbability(List<GameResult> history) {
    if (history.isEmpty) return 50.0;
    
    final crashes = history.where((r) => r.isCrash).length;
    return (crashes / history.length) * 100.0;
  }

  static double _calculateVolatilityTrend(List<GameResult> history) {
    if (history.length < 10) return 0.5;
    
    final recent = history.take(10).map((r) => r.multiplier).toList();
    final older = history.skip(10).take(10).map((r) => r.multiplier).toList();
    
    if (older.isEmpty) return 0.5;
    
    final recentVariance = _calculateVariance(recent);
    final olderVariance = _calculateVariance(older);
    
    return recentVariance / (olderVariance + 0.1); // Éviter division par zéro
  }

  static double _calculateVariance(List<double> values) {
    if (values.isEmpty) return 0.0;
    
    final mean = values.reduce((a, b) => a + b) / values.length;
    final variance = values
        .map((v) => pow(v - mean, 2))
        .reduce((a, b) => a + b) / values.length;
    
    return variance;
  }

  static String _recommendStrategy(List<GameResult> history) {
    final analysis = analyzeMultiplierProbabilities(history);
    final volatility = _calculateVolatilityTrend(history);
    
    if (volatility > 1.5) return 'CONSERVATIVE';
    if (volatility < 0.7) return 'AGGRESSIVE';
    return 'BALANCED';
  }

  static List<int> _findBestHours(Map<int, List<double>> hourlyDistribution) {
    final hourlyAverages = <int, double>{};
    
    for (final entry in hourlyDistribution.entries) {
      if (entry.value.isNotEmpty) {
        hourlyAverages[entry.key] = 
            entry.value.reduce((a, b) => a + b) / entry.value.length;
      }
    }
    
    final sortedHours = hourlyAverages.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedHours.take(3).map((e) => e.key).toList();
  }

  static List<int> _findBestDays(Map<int, List<double>> weeklyDistribution) {
    final weeklyAverages = <int, double>{};
    
    for (final entry in weeklyDistribution.entries) {
      if (entry.value.isNotEmpty) {
        weeklyAverages[entry.key] = 
            entry.value.reduce((a, b) => a + b) / entry.value.length;
      }
    }
    
    final sortedDays = weeklyAverages.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedDays.take(3).map((e) => e.key).toList();
  }
}

// Classes de données
class MultiplierRange {
  final double min;
  final double max;
  final String name;
  final String riskLevel;

  const MultiplierRange(this.min, this.max, this.name, this.riskLevel);
}

class MultiplierAnalysis {
  final int totalGames;
  final Map<MultiplierRange, double> rangeProbabilities;
  final Map<double, double> popularProbabilities;
  final Map<String, dynamic> generalStats;
  final Map<String, dynamic> futurePredictions;
  final Map<String, dynamic> temporalPatterns;
  final DateTime lastUpdated;

  MultiplierAnalysis({
    required this.totalGames,
    required this.rangeProbabilities,
    required this.popularProbabilities,
    required this.generalStats,
    required this.futurePredictions,
    required this.temporalPatterns,
    required this.lastUpdated,
  });

  factory MultiplierAnalysis.empty() {
    return MultiplierAnalysis(
      totalGames: 0,
      rangeProbabilities: {},
      popularProbabilities: {},
      generalStats: {},
      futurePredictions: {},
      temporalPatterns: {},
      lastUpdated: DateTime.now(),
    );
  }
}

class SequenceAnalysis {
  final Map<String, int> commonSequences;
  final Map<String, Map<String, int>> transitions;
  final int totalSequences;

  SequenceAnalysis({
    required this.commonSequences,
    required this.transitions,
    required this.totalSequences,
  });

  factory SequenceAnalysis.empty() {
    return SequenceAnalysis(
      commonSequences: {},
      transitions: {},
      totalSequences: 0,
    );
  }
}

class NextMultiplierPrediction {
  final double predictedMultiplier;
  final double confidence;
  final String category;
  final MultiplierRange range;
  final String basedOnPattern;
  final Map<String, double> alternatives;

  NextMultiplierPrediction({
    required this.predictedMultiplier,
    required this.confidence,
    required this.category,
    required this.range,
    required this.basedOnPattern,
    required this.alternatives,
  });

  factory NextMultiplierPrediction.default() {
    return NextMultiplierPrediction(
      predictedMultiplier: 2.0,
      confidence: 50.0,
      category: 'MEDIUM',
      range: const MultiplierRange(1.5, 3.0, 'Modéré', 'MEDIUM'),
      basedOnPattern: 'DEFAULT',
      alternatives: {},
    );
  }
}
