// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'game_result.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class GameResultAdapter extends TypeAdapter<GameResult> {
  @override
  final int typeId = 0;

  @override
  GameResult read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return GameResult(
      id: fields[0] as String,
      multiplier: fields[1] as double,
      timestamp: fields[2] as DateTime,
      betAmount: fields[3] as double?,
      isCrash: fields[4] as bool,
      profit: fields[5] as double?,
      strategy: fields[6] as String?,
      crashTimeSeconds: fields[7] as int?,
      volatilityAtTime: fields[8] as double?,
      gameSession: fields[9] as String?,
      metadata: fields[10] as Map<String, dynamic>?,
      isAutoSaved: fields[11] as bool,
      predictedMultiplier: fields[12] as double?,
      predictionAccuracy: fields[13] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, GameResult obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.multiplier)
      ..writeByte(2)
      ..write(obj.timestamp)
      ..writeByte(3)
      ..write(obj.betAmount)
      ..writeByte(4)
      ..write(obj.isCrash)
      ..writeByte(5)
      ..write(obj.profit)
      ..writeByte(6)
      ..write(obj.strategy)
      ..writeByte(7)
      ..write(obj.crashTimeSeconds)
      ..writeByte(8)
      ..write(obj.volatilityAtTime)
      ..writeByte(9)
      ..write(obj.gameSession)
      ..writeByte(10)
      ..write(obj.metadata)
      ..writeByte(11)
      ..write(obj.isAutoSaved)
      ..writeByte(12)
      ..write(obj.predictedMultiplier)
      ..writeByte(13)
      ..write(obj.predictionAccuracy);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GameResultAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
