@echo off
echo ========================================
echo   Aviator Predictor Pro - Build Script
echo ========================================
echo.

set PROJECT_NAME=aviator_predictor_flutter
set VERSION=2.0.0

echo Verification de l'environnement Flutter...
flutter --version
if %errorlevel% neq 0 (
    echo ERREUR: Flutter n'est pas installe ou pas dans le PATH
    pause
    exit /b 1
)

echo.
echo Nettoyage du projet...
flutter clean
flutter pub get

echo.
echo Generation des fichiers Hive...
flutter packages pub run build_runner build --delete-conflicting-outputs

echo.
echo ========================================
echo   Compilation pour toutes les plateformes
echo ========================================

echo.
echo [1/3] Compilation Web (PWA)...
echo ----------------------------------------
flutter build web --release --web-renderer canvaskit --base-href /
if %errorlevel% equ 0 (
    echo ✅ Build Web reussi
    echo Fichiers generes dans: build\web\
) else (
    echo ❌ Echec du build Web
)

echo.
echo [2/3] Compilation Windows Desktop...
echo ----------------------------------------
flutter build windows --release
if %errorlevel% equ 0 (
    echo ✅ Build Windows reussi
    echo Fichiers generes dans: build\windows\runner\Release\
) else (
    echo ❌ Echec du build Windows
)

echo.
echo [3/3] Compilation Android APK...
echo ----------------------------------------
flutter build apk --release --split-per-abi
if %errorlevel% equ 0 (
    echo ✅ Build Android reussi
    echo Fichiers generes dans: build\app\outputs\flutter-apk\
) else (
    echo ❌ Echec du build Android
)

echo.
echo ========================================
echo   Compilation Android App Bundle
echo ========================================
flutter build appbundle --release
if %errorlevel% equ 0 (
    echo ✅ Build App Bundle reussi
    echo Fichier genere dans: build\app\outputs\bundle\release\
) else (
    echo ❌ Echec du build App Bundle
)

echo.
echo ========================================
echo   Analyse du code
echo ========================================
echo Analyse statique du code...
flutter analyze
echo.
echo Tests unitaires...
flutter test

echo.
echo ========================================
echo   Rapport de build
echo ========================================
echo.
echo Projet: %PROJECT_NAME%
echo Version: %VERSION%
echo Date: %date% %time%
echo.

if exist "build\web\index.html" (
    echo ✅ Web PWA: PRET
    for %%I in ("build\web\*") do echo    Taille: %%~zI bytes
) else (
    echo ❌ Web PWA: ECHEC
)

if exist "build\windows\runner\Release\%PROJECT_NAME%.exe" (
    echo ✅ Windows Desktop: PRET
    for %%I in ("build\windows\runner\Release\%PROJECT_NAME%.exe") do echo    Taille: %%~zI bytes
) else (
    echo ❌ Windows Desktop: ECHEC
)

if exist "build\app\outputs\flutter-apk\app-arm64-v8a-release.apk" (
    echo ✅ Android APK: PRET
    for %%I in ("build\app\outputs\flutter-apk\app-arm64-v8a-release.apk") do echo    Taille: %%~zI bytes
) else (
    echo ❌ Android APK: ECHEC
)

if exist "build\app\outputs\bundle\release\app-release.aab" (
    echo ✅ Android Bundle: PRET
    for %%I in ("build\app\outputs\bundle\release\app-release.aab") do echo    Taille: %%~zI bytes
) else (
    echo ❌ Android Bundle: ECHEC
)

echo.
echo ========================================
echo   Instructions de deploiement
echo ========================================
echo.
echo Web PWA:
echo   1. Uploader le contenu de build\web\ vers votre serveur
echo   2. Configurer HTTPS pour PWA
echo   3. Tester l'installation PWA
echo.
echo Windows Desktop:
echo   1. Distribuer build\windows\runner\Release\
echo   2. Inclure les DLLs necessaires
echo   3. Creer un installateur (optionnel)
echo.
echo Android:
echo   1. APK: Distribuer directement ou via stores alternatifs
echo   2. AAB: Uploader sur Google Play Console
echo   3. Tester sur differents appareils
echo.

echo Build termine! Verifiez les fichiers generes.
pause
