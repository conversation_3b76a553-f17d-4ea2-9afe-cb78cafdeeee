import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive/hive.dart';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../../data/models/game_result.dart';
import '../../data/models/prediction.dart';
import '../../core/services/prediction_service.dart';
import '../../core/services/advanced_prediction_service.dart';
import '../../core/services/auto_save_service.dart';
import '../../core/services/crash_timing_service.dart';
import '../../core/services/multiplier_probability_service.dart';

// Game state class
class GameState {
  final List<GameResult> results;
  final List<Prediction> predictions;
  final Prediction? lastPrediction;
  final bool isLoading;
  final String? error;
  final double volatilityIndex;
  final double trendMomentum;
  final double riskScore;
  final List<String> alerts;
  final CrashTimingPrediction? currentCrashPrediction;
  final MultiplierAnalysis? multiplierAnalysis;
  final NextMultiplierPrediction? nextMultiplierPrediction;
  final bool isGameRunning;
  final bool isAutoMode;

  GameState({
    required this.results,
    required this.predictions,
    this.lastPrediction,
    this.isLoading = false,
    this.error,
    required this.volatilityIndex,
    required this.trendMomentum,
    required this.riskScore,
    required this.alerts,
    this.currentCrashPrediction,
    this.multiplierAnalysis,
    this.nextMultiplierPrediction,
    this.isGameRunning = false,
    this.isAutoMode = false,
  });

  GameState copyWith({
    List<GameResult>? results,
    List<Prediction>? predictions,
    Prediction? lastPrediction,
    bool? isLoading,
    String? error,
    double? volatilityIndex,
    double? trendMomentum,
    double? riskScore,
    List<String>? alerts,
    CrashTimingPrediction? currentCrashPrediction,
    MultiplierAnalysis? multiplierAnalysis,
    NextMultiplierPrediction? nextMultiplierPrediction,
    bool? isGameRunning,
    bool? isAutoMode,
  }) {
    return GameState(
      results: results ?? this.results,
      predictions: predictions ?? this.predictions,
      lastPrediction: lastPrediction ?? this.lastPrediction,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      volatilityIndex: volatilityIndex ?? this.volatilityIndex,
      trendMomentum: trendMomentum ?? this.trendMomentum,
      riskScore: riskScore ?? this.riskScore,
      alerts: alerts ?? this.alerts,
      currentCrashPrediction: currentCrashPrediction ?? this.currentCrashPrediction,
      multiplierAnalysis: multiplierAnalysis ?? this.multiplierAnalysis,
      nextMultiplierPrediction: nextMultiplierPrediction ?? this.nextMultiplierPrediction,
      isGameRunning: isGameRunning ?? this.isGameRunning,
      isAutoMode: isAutoMode ?? this.isAutoMode,
    );
  }

  // Computed properties
  double get averageMultiplier {
    if (results.isEmpty) return 0.0;
    return results.map((r) => r.multiplier).reduce((a, b) => a + b) / results.length;
  }

  double get averageAccuracy {
    if (predictions.isEmpty) return 0.0;
    final accuratePredictions = predictions.where((p) => p.accuracy != null && p.accuracy! > 0).toList();
    if (accuratePredictions.isEmpty) return 0.0;
    return accuratePredictions.map((p) => p.accuracy!).reduce((a, b) => a + b) / accuratePredictions.length;
  }

  double get crashRate {
    if (results.isEmpty) return 0.0;
    final crashes = results.where((r) => r.isCrash).length;
    return crashes / results.length;
  }

  double get totalProfit {
    return results
        .where((r) => r.profit != null)
        .map((r) => r.profit!)
        .fold(0.0, (a, b) => a + b);
  }

  double get winRate {
    if (results.isEmpty) return 0.0;
    final wins = results.where((r) => r.profit != null && r.profit! > 0).length;
    return wins / results.length;
  }

  int get currentStreak {
    if (results.isEmpty) return 0;
    
    int streak = 0;
    final isWinning = results.first.profit != null && results.first.profit! > 0;
    
    for (final result in results) {
      if (result.profit == null) continue;
      
      final isWin = result.profit! > 0;
      if (isWin == isWinning) {
        streak++;
      } else {
        break;
      }
    }
    
    return isWinning ? streak : -streak;
  }
}

// Game state notifier
class GameNotifier extends StateNotifier<GameState> {
  late Box<GameResult> _resultsBox;
  late Box<Prediction> _predictionsBox;
  late AutoSaveService _autoSaveService;

  GameNotifier() : super(GameState(
    results: [],
    predictions: [],
    volatilityIndex: 0.0,
    trendMomentum: 0.0,
    riskScore: 0.0,
    alerts: [],
  )) {
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    _autoSaveService = AutoSaveService();
    await _autoSaveService.initialize();

    _resultsBox = await Hive.openBox<GameResult>('game_results');
    _predictionsBox = await Hive.openBox<Prediction>('predictions');
    _loadData();
  }

  void _loadData() {
    final results = _resultsBox.values.toList();
    final predictions = _predictionsBox.values.toList();
    
    // Sort by timestamp (most recent first)
    results.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    predictions.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    final metrics = _calculateMetrics(results);
    final alerts = _generateAlerts(results, predictions.isNotEmpty ? predictions.first : null);
    
    state = state.copyWith(
      results: results,
      predictions: predictions,
      lastPrediction: predictions.isNotEmpty ? predictions.first : null,
      volatilityIndex: metrics['volatility'] ?? 0.0,
      trendMomentum: metrics['momentum'] ?? 0.0,
      riskScore: metrics['riskScore'] ?? 0.0,
      alerts: alerts,
    );
  }

  // Ajouter un résultat avec sauvegarde automatique
  Future<void> addResult(GameResult result) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Sauvegarder automatiquement avec le service
      await _autoSaveService.saveGameResult(result);
      await _resultsBox.add(result);

      // Update last prediction if it exists
      if (state.lastPrediction != null) {
        final updatedPrediction = state.lastPrediction!.evaluate(result.multiplier);
        await _predictionsBox.put(state.lastPrediction!.key, updatedPrediction);
      }

      // Recalculer toutes les analyses
      await _updateAnalyses();

      _loadData();

    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Erreur lors de l\'ajout du résultat: $e',
      );
    }
  }

  // Méthode pour ajouter un résultat simple (compatibilité)
  Future<void> addSimpleResult(double multiplier, {double? betAmount, String? strategy}) async {
    final result = GameResult.create(
      multiplier: multiplier,
      betAmount: betAmount,
      strategy: strategy,
    );
    await addResult(result);
  }

  Future<void> makePrediction({String method = 'hybrid'}) async {
    if (state.results.length < 10) {
      state = state.copyWith(
        error: 'Au moins 10 résultats sont nécessaires pour une prédiction fiable',
      );
      return;
    }

    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final prediction = PredictionService.predictNext(state.results, method: method);
      await _predictionsBox.add(prediction);
      
      final metrics = _calculateMetrics(state.results);
      final alerts = _generateAlerts(state.results, prediction);
      
      state = state.copyWith(
        isLoading: false,
        lastPrediction: prediction,
        predictions: [prediction, ...state.predictions],
        volatilityIndex: metrics['volatility'] ?? 0.0,
        trendMomentum: metrics['momentum'] ?? 0.0,
        riskScore: metrics['riskScore'] ?? 0.0,
        alerts: alerts,
      );
      
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Erreur lors de la prédiction: $e',
      );
    }
  }

  Future<void> makeAdvancedPrediction(String algorithmType) async {
    if (state.results.length < 15) {
      state = state.copyWith(
        error: 'Au moins 15 résultats sont nécessaires pour une prédiction avancée',
      );
      return;
    }

    state = state.copyWith(isLoading: true, error: null);
    
    try {
      Prediction prediction;
      
      switch (algorithmType) {
        case 'lstm':
          prediction = AdvancedPredictionService.lstmInspiredPrediction(state.results);
          break;
        case 'ensemble':
          prediction = AdvancedPredictionService.ensemblePrediction(state.results);
          break;
        case 'reinforcement':
          prediction = AdvancedPredictionService.reinforcementLearningPrediction(state.results);
          break;
        case 'genetic':
          prediction = AdvancedPredictionService.geneticAlgorithmPrediction(state.results);
          break;
        case 'quantum':
          prediction = AdvancedPredictionService.quantumInspiredPrediction(state.results);
          break;
        default:
          prediction = PredictionService.predictNext(state.results, method: 'advanced_hybrid');
      }
      
      await _predictionsBox.add(prediction);
      
      final metrics = _calculateMetrics(state.results);
      final alerts = _generateAlerts(state.results, prediction);
      
      state = state.copyWith(
        isLoading: false,
        lastPrediction: prediction,
        predictions: [prediction, ...state.predictions],
        volatilityIndex: metrics['volatility'] ?? 0.0,
        trendMomentum: metrics['momentum'] ?? 0.0,
        riskScore: metrics['riskScore'] ?? 0.0,
        alerts: alerts,
      );
      
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Erreur lors de la prédiction avancée: $e',
      );
    }
  }

  // Nouvelles méthodes pour les fonctionnalités avancées
  Future<void> _updateAnalyses() async {
    final results = _autoSaveService.getAllResults();

    // Prédiction de timing de crash
    final crashPrediction = CrashTimingService.predictCrashTiming(results);

    // Analyse des multiplicateurs
    final multiplierAnalysis = MultiplierProbabilityService.analyzeMultiplierProbabilities(results);

    // Prédiction du prochain multiplicateur
    final nextMultiplierPrediction = MultiplierProbabilityService.predictNextMultiplier(results);

    state = state.copyWith(
      currentCrashPrediction: crashPrediction,
      multiplierAnalysis: multiplierAnalysis,
      nextMultiplierPrediction: nextMultiplierPrediction,
    );
  }

  // Prédire le timing de crash pour un multiplicateur spécifique
  Future<CrashTimingPrediction> predictCrashTiming({double? targetMultiplier, String method = 'hybrid'}) async {
    final results = _autoSaveService.getAllResults();
    return CrashTimingService.predictCrashTiming(results, targetMultiplier: targetMultiplier, method: method);
  }

  // Calculer la probabilité d'un multiplicateur spécifique
  double calculateMultiplierProbability(double targetMultiplier) {
    final results = _autoSaveService.getAllResults();
    return MultiplierProbabilityService.calculateSpecificMultiplierProbability(targetMultiplier, results);
  }

  // Prédire la probabilité dans les N prochaines parties
  double predictProbabilityInNextGames(double targetMultiplier, int nextGames) {
    final results = _autoSaveService.getAllResults();
    return MultiplierProbabilityService.predictProbabilityInNextGames(targetMultiplier, results, nextGames);
  }

  // Contrôles de jeu
  void startGame() {
    state = state.copyWith(isGameRunning: true);
  }

  void stopGame() {
    state = state.copyWith(isGameRunning: false);
  }

  void toggleAutoMode() {
    state = state.copyWith(isAutoMode: !state.isAutoMode);
  }

  // Gestion des données
  Future<void> clearHistory() async {
    await _resultsBox.clear();
    await _predictionsBox.clear();

    state = GameState(
      results: [],
      predictions: [],
      volatilityIndex: 0.0,
      trendMomentum: 0.0,
      riskScore: 0.0,
      alerts: [],
    );
  }

  Future<void> exportData() async {
    try {
      await _autoSaveService.createBackup();
    } catch (e) {
      state = state.copyWith(error: 'Erreur lors de l\'export: $e');
    }
  }

  Future<void> importData() async {
    try {
      await _autoSaveService.restoreFromBackup();
      _loadData();
    } catch (e) {
      state = state.copyWith(error: 'Erreur lors de l\'import: $e');
    }
  }

  Map<String, double> _calculateMetrics(List<GameResult> results) {
    if (results.length < 5) {
      return {'volatility': 0.0, 'momentum': 0.0, 'riskScore': 0.0};
    }

    final multipliers = results.take(20).map((r) => r.multiplier).toList();
    
    // Volatility calculation
    final mean = multipliers.reduce((a, b) => a + b) / multipliers.length;
    final variance = multipliers.map((x) => (x - mean) * (x - mean)).reduce((a, b) => a + b) / multipliers.length;
    final volatility = (variance > 0) ? (variance.sqrt() / mean) : 0.0;
    
    // Momentum calculation
    double momentum = 0.0;
    if (multipliers.length > 1) {
      for (int i = 1; i < multipliers.length; i++) {
        momentum += multipliers[i] - multipliers[i - 1];
      }
      momentum /= (multipliers.length - 1);
    }
    
    // Risk score calculation
    final crashRate = multipliers.where((x) => x <= 1.1).length / multipliers.length;
    final riskScore = (volatility * 0.4 + crashRate * 0.6) * 100;
    
    return {
      'volatility': volatility,
      'momentum': momentum,
      'riskScore': riskScore,
    };
  }

  List<String> _generateAlerts(List<GameResult> results, Prediction? prediction) {
    final alerts = <String>[];
    
    if (prediction != null) {
      if (prediction.crashProbability > 0.8) {
        alerts.add('🚨 Probabilité de crash très élevée (${(prediction.crashProbability * 100).toStringAsFixed(0)}%)');
      } else if (prediction.crashProbability > 0.6) {
        alerts.add('⚠️ Risque de crash élevé (${(prediction.crashProbability * 100).toStringAsFixed(0)}%)');
      }
      
      if (prediction.volatilityIndex > 0.6) {
        alerts.add('📊 Volatilité extrême détectée');
      }
      
      if (prediction.confidence < 50) {
        alerts.add('🎯 Confiance de prédiction faible');
      }
    }
    
    if (results.isNotEmpty) {
      final recentCrashes = results.take(5).where((r) => r.isCrash).length;
      if (recentCrashes >= 3) {
        alerts.add('💥 Série de crashs détectée');
      }
      
      final recentHighs = results.take(5).where((r) => r.multiplier > 5.0).length;
      if (recentHighs >= 3) {
        alerts.add('🚀 Série de multiplicateurs élevés');
      }
    }
    
    return alerts;
  }
}

// Provider
final gameProvider = StateNotifierProvider<GameNotifier, GameState>((ref) {
  return GameNotifier();
});
