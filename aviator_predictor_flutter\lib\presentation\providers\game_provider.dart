import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive/hive.dart';
import '../../data/models/game_result.dart';
import '../../data/models/prediction.dart';
import '../../core/services/prediction_service.dart';
import '../../core/services/advanced_prediction_service.dart';

// Game state class
class GameState {
  final List<GameResult> results;
  final List<Prediction> predictions;
  final Prediction? lastPrediction;
  final bool isLoading;
  final String? error;
  final double volatilityIndex;
  final double trendMomentum;
  final double riskScore;
  final List<String> alerts;

  GameState({
    required this.results,
    required this.predictions,
    this.lastPrediction,
    this.isLoading = false,
    this.error,
    required this.volatilityIndex,
    required this.trendMomentum,
    required this.riskScore,
    required this.alerts,
  });

  GameState copyWith({
    List<GameResult>? results,
    List<Prediction>? predictions,
    Prediction? lastPrediction,
    bool? isLoading,
    String? error,
    double? volatilityIndex,
    double? trendMomentum,
    double? riskScore,
    List<String>? alerts,
  }) {
    return GameState(
      results: results ?? this.results,
      predictions: predictions ?? this.predictions,
      lastPrediction: lastPrediction ?? this.lastPrediction,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      volatilityIndex: volatilityIndex ?? this.volatilityIndex,
      trendMomentum: trendMomentum ?? this.trendMomentum,
      riskScore: riskScore ?? this.riskScore,
      alerts: alerts ?? this.alerts,
    );
  }

  // Computed properties
  double get averageMultiplier {
    if (results.isEmpty) return 0.0;
    return results.map((r) => r.multiplier).reduce((a, b) => a + b) / results.length;
  }

  double get crashRate {
    if (results.isEmpty) return 0.0;
    final crashes = results.where((r) => r.isCrash).length;
    return crashes / results.length;
  }

  double get totalProfit {
    return results
        .where((r) => r.profit != null)
        .map((r) => r.profit!)
        .fold(0.0, (a, b) => a + b);
  }

  double get winRate {
    if (results.isEmpty) return 0.0;
    final wins = results.where((r) => r.profit != null && r.profit! > 0).length;
    return wins / results.length;
  }

  int get currentStreak {
    if (results.isEmpty) return 0;
    
    int streak = 0;
    final isWinning = results.first.profit != null && results.first.profit! > 0;
    
    for (final result in results) {
      if (result.profit == null) continue;
      
      final isWin = result.profit! > 0;
      if (isWin == isWinning) {
        streak++;
      } else {
        break;
      }
    }
    
    return isWinning ? streak : -streak;
  }
}

// Game state notifier
class GameNotifier extends StateNotifier<GameState> {
  late Box<GameResult> _resultsBox;
  late Box<Prediction> _predictionsBox;

  GameNotifier() : super(GameState(
    results: [],
    predictions: [],
    volatilityIndex: 0.0,
    trendMomentum: 0.0,
    riskScore: 0.0,
    alerts: [],
  )) {
    _initializeBoxes();
  }

  Future<void> _initializeBoxes() async {
    _resultsBox = await Hive.openBox<GameResult>('game_results');
    _predictionsBox = await Hive.openBox<Prediction>('predictions');
    _loadData();
  }

  void _loadData() {
    final results = _resultsBox.values.toList();
    final predictions = _predictionsBox.values.toList();
    
    // Sort by timestamp (most recent first)
    results.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    predictions.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    final metrics = _calculateMetrics(results);
    final alerts = _generateAlerts(results, predictions.isNotEmpty ? predictions.first : null);
    
    state = state.copyWith(
      results: results,
      predictions: predictions,
      lastPrediction: predictions.isNotEmpty ? predictions.first : null,
      volatilityIndex: metrics['volatility'] ?? 0.0,
      trendMomentum: metrics['momentum'] ?? 0.0,
      riskScore: metrics['riskScore'] ?? 0.0,
      alerts: alerts,
    );
  }

  Future<void> addResult(double multiplier, {double? betAmount, String? strategy}) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final result = GameResult.create(
        multiplier: multiplier,
        betAmount: betAmount,
        strategy: strategy,
      );
      
      await _resultsBox.add(result);
      
      // Update last prediction if it exists
      if (state.lastPrediction != null) {
        final updatedPrediction = state.lastPrediction!.evaluate(multiplier);
        await _predictionsBox.put(state.lastPrediction!.key, updatedPrediction);
      }
      
      _loadData();
      
      // Auto-generate new prediction if enabled
      // This could be controlled by a setting
      await makePrediction();
      
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Erreur lors de l\'ajout du résultat: $e',
      );
    }
  }

  Future<void> makePrediction({String method = 'hybrid'}) async {
    if (state.results.length < 10) {
      state = state.copyWith(
        error: 'Au moins 10 résultats sont nécessaires pour une prédiction fiable',
      );
      return;
    }

    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final prediction = PredictionService.predictNext(state.results, method: method);
      await _predictionsBox.add(prediction);
      
      final metrics = _calculateMetrics(state.results);
      final alerts = _generateAlerts(state.results, prediction);
      
      state = state.copyWith(
        isLoading: false,
        lastPrediction: prediction,
        predictions: [prediction, ...state.predictions],
        volatilityIndex: metrics['volatility'] ?? 0.0,
        trendMomentum: metrics['momentum'] ?? 0.0,
        riskScore: metrics['riskScore'] ?? 0.0,
        alerts: alerts,
      );
      
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Erreur lors de la prédiction: $e',
      );
    }
  }

  Future<void> makeAdvancedPrediction(String algorithmType) async {
    if (state.results.length < 15) {
      state = state.copyWith(
        error: 'Au moins 15 résultats sont nécessaires pour une prédiction avancée',
      );
      return;
    }

    state = state.copyWith(isLoading: true, error: null);
    
    try {
      Prediction prediction;
      
      switch (algorithmType) {
        case 'lstm':
          prediction = AdvancedPredictionService.lstmInspiredPrediction(state.results);
          break;
        case 'ensemble':
          prediction = AdvancedPredictionService.ensemblePrediction(state.results);
          break;
        case 'reinforcement':
          prediction = AdvancedPredictionService.reinforcementLearningPrediction(state.results);
          break;
        case 'genetic':
          prediction = AdvancedPredictionService.geneticAlgorithmPrediction(state.results);
          break;
        case 'quantum':
          prediction = AdvancedPredictionService.quantumInspiredPrediction(state.results);
          break;
        default:
          prediction = PredictionService.predictNext(state.results, method: 'advanced_hybrid');
      }
      
      await _predictionsBox.add(prediction);
      
      final metrics = _calculateMetrics(state.results);
      final alerts = _generateAlerts(state.results, prediction);
      
      state = state.copyWith(
        isLoading: false,
        lastPrediction: prediction,
        predictions: [prediction, ...state.predictions],
        volatilityIndex: metrics['volatility'] ?? 0.0,
        trendMomentum: metrics['momentum'] ?? 0.0,
        riskScore: metrics['riskScore'] ?? 0.0,
        alerts: alerts,
      );
      
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Erreur lors de la prédiction avancée: $e',
      );
    }
  }

  Future<void> clearData() async {
    await _resultsBox.clear();
    await _predictionsBox.clear();
    
    state = GameState(
      results: [],
      predictions: [],
      volatilityIndex: 0.0,
      trendMomentum: 0.0,
      riskScore: 0.0,
      alerts: [],
    );
  }

  Map<String, double> _calculateMetrics(List<GameResult> results) {
    if (results.length < 5) {
      return {'volatility': 0.0, 'momentum': 0.0, 'riskScore': 0.0};
    }

    final multipliers = results.take(20).map((r) => r.multiplier).toList();
    
    // Volatility calculation
    final mean = multipliers.reduce((a, b) => a + b) / multipliers.length;
    final variance = multipliers.map((x) => (x - mean) * (x - mean)).reduce((a, b) => a + b) / multipliers.length;
    final volatility = (variance > 0) ? (variance.sqrt() / mean) : 0.0;
    
    // Momentum calculation
    double momentum = 0.0;
    if (multipliers.length > 1) {
      for (int i = 1; i < multipliers.length; i++) {
        momentum += multipliers[i] - multipliers[i - 1];
      }
      momentum /= (multipliers.length - 1);
    }
    
    // Risk score calculation
    final crashRate = multipliers.where((x) => x <= 1.1).length / multipliers.length;
    final riskScore = (volatility * 0.4 + crashRate * 0.6) * 100;
    
    return {
      'volatility': volatility,
      'momentum': momentum,
      'riskScore': riskScore,
    };
  }

  List<String> _generateAlerts(List<GameResult> results, Prediction? prediction) {
    final alerts = <String>[];
    
    if (prediction != null) {
      if (prediction.crashProbability > 0.8) {
        alerts.add('🚨 Probabilité de crash très élevée (${(prediction.crashProbability * 100).toStringAsFixed(0)}%)');
      } else if (prediction.crashProbability > 0.6) {
        alerts.add('⚠️ Risque de crash élevé (${(prediction.crashProbability * 100).toStringAsFixed(0)}%)');
      }
      
      if (prediction.volatilityIndex > 0.6) {
        alerts.add('📊 Volatilité extrême détectée');
      }
      
      if (prediction.confidence < 50) {
        alerts.add('🎯 Confiance de prédiction faible');
      }
    }
    
    if (results.isNotEmpty) {
      final recentCrashes = results.take(5).where((r) => r.isCrash).length;
      if (recentCrashes >= 3) {
        alerts.add('💥 Série de crashs détectée');
      }
      
      final recentHighs = results.take(5).where((r) => r.multiplier > 5.0).length;
      if (recentHighs >= 3) {
        alerts.add('🚀 Série de multiplicateurs élevés');
      }
    }
    
    return alerts;
  }
}

// Provider
final gameProvider = StateNotifierProvider<GameNotifier, GameState>((ref) {
  return GameNotifier();
});
