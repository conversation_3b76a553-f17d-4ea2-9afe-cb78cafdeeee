import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_theme.dart';
import '../providers/game_provider.dart';
import 'add_result_dialog.dart';

class ControlPanel extends ConsumerStatefulWidget {
  const ControlPanel({super.key});

  @override
  ConsumerState<ControlPanel> createState() => _ControlPanelState();
}

class _ControlPanelState extends ConsumerState<ControlPanel> 
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;
  
  bool _isGameRunning = false;
  bool _isAutoMode = false;
  String _selectedAlgorithm = 'hybrid';

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameProvider);
    
    return Card(
      elevation: 8,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).cardColor,
              Theme.of(context).cardColor.withOpacity(0.8),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 20),
            _buildMainControls(gameState),
            const SizedBox(height: 16),
            _buildAlgorithmSelector(),
            const SizedBox(height: 16),
            _buildQuickActions(gameState),
            const SizedBox(height: 16),
            _buildGameStatus(gameState),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.control_camera,
            color: Colors.black,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Panneau de Contrôle',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Contrôlez vos prédictions et analyses',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: _isAutoMode,
          onChanged: (value) {
            setState(() {
              _isAutoMode = value;
            });
            if (value) {
              _startAutoMode();
            } else {
              _stopAutoMode();
            }
          },
          activeColor: AppTheme.successColor,
        ),
        const SizedBox(width: 8),
        Text(
          'Auto',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildMainControls(GameState gameState) {
    return Row(
      children: [
        // Bouton Start/Stop principal
        Expanded(
          flex: 2,
          child: AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _isGameRunning ? _pulseAnimation.value : 1.0,
                child: ElevatedButton.icon(
                  onPressed: () => _toggleGameState(),
                  icon: AnimatedBuilder(
                    animation: _rotationAnimation,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle: _isGameRunning ? _rotationAnimation.value * 2 * 3.14159 : 0,
                        child: Icon(
                          _isGameRunning ? Icons.stop : Icons.play_arrow,
                          size: 28,
                        ),
                      );
                    },
                  ),
                  label: Text(
                    _isGameRunning ? 'ARRÊTER' : 'DÉMARRER',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isGameRunning ? AppTheme.errorColor : AppTheme.successColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(width: 12),
        
        // Bouton Prédiction
        Expanded(
          child: ElevatedButton.icon(
            onPressed: gameState.isLoading ? null : () => _makePrediction(),
            icon: gameState.isLoading 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.psychology),
            label: const Text('Prédire'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        
        // Bouton Ajouter Résultat
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _showAddResultDialog(),
            icon: const Icon(Icons.add),
            label: const Text('Ajouter'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.secondaryColor,
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAlgorithmSelector() {
    final algorithms = [
      {'id': 'hybrid', 'name': 'Hybride', 'icon': Icons.auto_awesome},
      {'id': 'lstm', 'name': 'LSTM', 'icon': Icons.psychology},
      {'id': 'ensemble', 'name': 'Ensemble', 'icon': Icons.group_work},
      {'id': 'reinforcement', 'name': 'RL', 'icon': Icons.smart_toy},
      {'id': 'genetic', 'name': 'Génétique', 'icon': Icons.dna},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Algorithme de Prédiction',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: algorithms.map((algorithm) {
            final isSelected = _selectedAlgorithm == algorithm['id'];
            return FilterChip(
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _selectedAlgorithm = algorithm['id'] as String;
                  });
                }
              },
              avatar: Icon(
                algorithm['icon'] as IconData,
                size: 18,
                color: isSelected ? Colors.black : null,
              ),
              label: Text(algorithm['name'] as String),
              selectedColor: AppTheme.primaryColor,
              checkmarkColor: Colors.black,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildQuickActions(GameState gameState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Actions Rapides',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _clearHistory(),
                icon: const Icon(Icons.clear_all, size: 18),
                label: const Text('Effacer'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppTheme.errorColor,
                  side: const BorderSide(color: AppTheme.errorColor),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _exportData(),
                icon: const Icon(Icons.download, size: 18),
                label: const Text('Exporter'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                  side: const BorderSide(color: AppTheme.primaryColor),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _importData(),
                icon: const Icon(Icons.upload, size: 18),
                label: const Text('Importer'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppTheme.secondaryColor,
                  side: const BorderSide(color: AppTheme.secondaryColor),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGameStatus(GameState gameState) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isGameRunning ? AppTheme.successColor : Colors.grey.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: _isGameRunning ? AppTheme.successColor : Colors.grey,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                _isGameRunning ? 'Jeu en cours' : 'Jeu arrêté',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (_isAutoMode)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'AUTO',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildStatusItem('Résultats', '${gameState.results.length}'),
              _buildStatusItem('Prédictions', '${gameState.predictions.length}'),
              _buildStatusItem('Précision', '${gameState.averageAccuracy.toStringAsFixed(1)}%'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  void _toggleGameState() {
    setState(() {
      _isGameRunning = !_isGameRunning;
    });

    if (_isGameRunning) {
      _pulseController.repeat(reverse: true);
      _rotationController.repeat();
      _startGame();
    } else {
      _pulseController.stop();
      _rotationController.stop();
      _stopGame();
    }
  }

  void _startGame() {
    // Logique pour démarrer le jeu
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🚀 Jeu démarré! Prédictions en cours...'),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  void _stopGame() {
    // Logique pour arrêter le jeu
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('⏹️ Jeu arrêté'),
        backgroundColor: AppTheme.errorColor,
      ),
    );
  }

  void _startAutoMode() {
    // Logique pour le mode automatique
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🤖 Mode automatique activé'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  void _stopAutoMode() {
    // Logique pour arrêter le mode automatique
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('👤 Mode manuel activé'),
        backgroundColor: AppTheme.warningColor,
      ),
    );
  }

  void _makePrediction() {
    ref.read(gameProvider.notifier).makeAdvancedPrediction(_selectedAlgorithm);
  }

  void _showAddResultDialog() {
    showDialog(
      context: context,
      builder: (context) => const AddResultDialog(),
    );
  }

  void _clearHistory() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: const Text('Êtes-vous sûr de vouloir effacer tout l\'historique ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(gameProvider.notifier).clearHistory();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Historique effacé'),
                  backgroundColor: AppTheme.successColor,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.errorColor),
            child: const Text('Effacer'),
          ),
        ],
      ),
    );
  }

  void _exportData() {
    ref.read(gameProvider.notifier).exportData();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Données exportées avec succès'),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  void _importData() {
    ref.read(gameProvider.notifier).importData();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Données importées avec succès'),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }
}
