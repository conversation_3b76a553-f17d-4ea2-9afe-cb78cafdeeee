import 'dart:math';
import '../../data/models/game_result.dart';

class CrashTimingService {
  static const double _baseGrowthRate = 0.1; // Taux de croissance de base par seconde
  static const double _volatilityFactor = 0.05; // Facteur de volatilité
  static const int _maxGameDuration = 300; // Durée maximale d'une partie (5 minutes)

  // Prédire le moment exact du crash basé sur l'historique
  static CrashTimingPrediction predictCrashTiming(List<GameResult> history, {
    double? targetMultiplier,
    String method = 'hybrid',
  }) {
    if (history.isEmpty) {
      return CrashTimingPrediction.empty();
    }

    switch (method) {
      case 'statistical':
        return _statisticalCrashPrediction(history, targetMultiplier);
      case 'pattern':
        return _patternBasedCrashPrediction(history, targetMultiplier);
      case 'volatility':
        return _volatilityBasedCrashPrediction(history, targetMultiplier);
      case 'machine_learning':
        return _mlBasedCrashPrediction(history, targetMultiplier);
      case 'hybrid':
      default:
        return _hybridCrashPrediction(history, targetMultiplier);
    }
  }

  // Prédiction statistique basée sur les moyennes historiques
  static CrashTimingPrediction _statisticalCrashPrediction(
    List<GameResult> history, 
    double? targetMultiplier
  ) {
    final validResults = history
        .where((r) => r.crashTimeSeconds != null)
        .toList();

    if (validResults.isEmpty) {
      return CrashTimingPrediction.empty();
    }

    // Si un multiplicateur cible est spécifié, filtrer les résultats similaires
    List<GameResult> relevantResults = validResults;
    if (targetMultiplier != null) {
      relevantResults = validResults
          .where((r) => (r.multiplier - targetMultiplier).abs() <= 1.0)
          .toList();
    }

    if (relevantResults.isEmpty) {
      relevantResults = validResults;
    }

    // Calculer les statistiques
    final crashTimes = relevantResults.map((r) => r.crashTimeSeconds!).toList();
    final averageTime = crashTimes.reduce((a, b) => a + b) / crashTimes.length;
    final variance = crashTimes
        .map((t) => pow(t - averageTime, 2))
        .reduce((a, b) => a + b) / crashTimes.length;
    final standardDeviation = sqrt(variance);

    // Prédire le temps de crash
    final predictedTime = averageTime.round();
    final confidence = _calculateConfidence(standardDeviation, crashTimes.length);

    // Calculer le multiplicateur prédit basé sur le temps
    final predictedMultiplier = _calculateMultiplierFromTime(predictedTime);

    return CrashTimingPrediction(
      predictedCrashTime: predictedTime,
      predictedMultiplier: predictedMultiplier,
      confidence: confidence,
      method: 'statistical',
      riskLevel: _calculateRiskLevel(predictedMultiplier),
      timeRange: _calculateTimeRange(averageTime, standardDeviation),
      volatilityIndex: _calculateVolatilityFromHistory(relevantResults),
    );
  }

  // Prédiction basée sur les patterns temporels
  static CrashTimingPrediction _patternBasedCrashPrediction(
    List<GameResult> history, 
    double? targetMultiplier
  ) {
    final recentResults = history.take(20).toList();
    
    // Analyser les patterns de timing
    final patterns = _analyzeTimingPatterns(recentResults);
    
    // Prédire basé sur les patterns détectés
    final predictedTime = patterns['averageInterval'] ?? 15;
    final confidence = patterns['patternStrength'] ?? 50.0;
    
    final predictedMultiplier = _calculateMultiplierFromTime(predictedTime);

    return CrashTimingPrediction(
      predictedCrashTime: predictedTime,
      predictedMultiplier: predictedMultiplier,
      confidence: confidence,
      method: 'pattern',
      riskLevel: _calculateRiskLevel(predictedMultiplier),
      timeRange: [predictedTime - 5, predictedTime + 5],
      volatilityIndex: patterns['volatility'] ?? 0.5,
    );
  }

  // Prédiction basée sur la volatilité
  static CrashTimingPrediction _volatilityBasedCrashPrediction(
    List<GameResult> history, 
    double? targetMultiplier
  ) {
    final volatility = _calculateVolatilityFromHistory(history);
    
    // Plus la volatilité est élevée, plus le crash est imprévisible
    final baseTime = 20; // Temps de base
    final volatilityAdjustment = (volatility * 30).round(); // Ajustement basé sur la volatilité
    
    final predictedTime = baseTime + volatilityAdjustment;
    final confidence = max(20.0, 90.0 - (volatility * 100));
    
    final predictedMultiplier = _calculateMultiplierFromTime(predictedTime);

    return CrashTimingPrediction(
      predictedCrashTime: predictedTime,
      predictedMultiplier: predictedMultiplier,
      confidence: confidence,
      method: 'volatility',
      riskLevel: _calculateRiskLevel(predictedMultiplier),
      timeRange: [predictedTime - 10, predictedTime + 10],
      volatilityIndex: volatility,
    );
  }

  // Prédiction basée sur machine learning simulé
  static CrashTimingPrediction _mlBasedCrashPrediction(
    List<GameResult> history, 
    double? targetMultiplier
  ) {
    // Simulation d'un modèle de machine learning
    final features = _extractFeatures(history);
    
    // Algorithme de régression simulé
    final predictedTime = _simulateMLRegression(features);
    final confidence = _simulateMLConfidence(features);
    
    final predictedMultiplier = _calculateMultiplierFromTime(predictedTime);

    return CrashTimingPrediction(
      predictedCrashTime: predictedTime,
      predictedMultiplier: predictedMultiplier,
      confidence: confidence,
      method: 'machine_learning',
      riskLevel: _calculateRiskLevel(predictedMultiplier),
      timeRange: [predictedTime - 3, predictedTime + 3],
      volatilityIndex: features['volatility'] ?? 0.5,
    );
  }

  // Prédiction hybride combinant toutes les méthodes
  static CrashTimingPrediction _hybridCrashPrediction(
    List<GameResult> history, 
    double? targetMultiplier
  ) {
    final statistical = _statisticalCrashPrediction(history, targetMultiplier);
    final pattern = _patternBasedCrashPrediction(history, targetMultiplier);
    final volatility = _volatilityBasedCrashPrediction(history, targetMultiplier);
    final ml = _mlBasedCrashPrediction(history, targetMultiplier);

    // Pondération des prédictions
    final weights = {
      'statistical': 0.3,
      'pattern': 0.25,
      'volatility': 0.2,
      'ml': 0.25,
    };

    final weightedTime = (
      statistical.predictedCrashTime * weights['statistical']! +
      pattern.predictedCrashTime * weights['pattern']! +
      volatility.predictedCrashTime * weights['volatility']! +
      ml.predictedCrashTime * weights['ml']!
    ).round();

    final weightedConfidence = (
      statistical.confidence * weights['statistical']! +
      pattern.confidence * weights['pattern']! +
      volatility.confidence * weights['volatility']! +
      ml.confidence * weights['ml']!
    );

    final predictedMultiplier = _calculateMultiplierFromTime(weightedTime);

    return CrashTimingPrediction(
      predictedCrashTime: weightedTime,
      predictedMultiplier: predictedMultiplier,
      confidence: weightedConfidence,
      method: 'hybrid',
      riskLevel: _calculateRiskLevel(predictedMultiplier),
      timeRange: [weightedTime - 5, weightedTime + 5],
      volatilityIndex: (statistical.volatilityIndex + pattern.volatilityIndex + 
                      volatility.volatilityIndex + ml.volatilityIndex) / 4,
    );
  }

  // Calculer le multiplicateur basé sur le temps
  static double _calculateMultiplierFromTime(int timeSeconds) {
    // Formule: multiplier = 1 + (time * growth_rate) + random_factor
    final baseMultiplier = 1.0 + (timeSeconds * _baseGrowthRate);
    final randomFactor = Random().nextDouble() * 0.5; // Facteur aléatoire
    return double.parse((baseMultiplier + randomFactor).toStringAsFixed(2));
  }

  // Calculer la confiance basée sur la variance
  static double _calculateConfidence(double standardDeviation, int sampleSize) {
    final baseConfidence = 100.0 - (standardDeviation * 2);
    final sampleBonus = min(20.0, sampleSize * 0.5); // Bonus pour échantillon large
    return max(10.0, min(95.0, baseConfidence + sampleBonus));
  }

  // Calculer le niveau de risque
  static String _calculateRiskLevel(double multiplier) {
    if (multiplier >= 10.0) return 'EXTREME';
    if (multiplier >= 5.0) return 'HIGH';
    if (multiplier >= 2.0) return 'MEDIUM';
    return 'LOW';
  }

  // Calculer la plage de temps
  static List<int> _calculateTimeRange(double averageTime, double standardDeviation) {
    final margin = (standardDeviation * 1.5).round();
    return [
      max(1, (averageTime - margin).round()),
      min(_maxGameDuration, (averageTime + margin).round()),
    ];
  }

  // Calculer la volatilité de l'historique
  static double _calculateVolatilityFromHistory(List<GameResult> history) {
    if (history.length < 2) return 0.5;

    final multipliers = history.map((r) => r.multiplier).toList();
    final average = multipliers.reduce((a, b) => a + b) / multipliers.length;
    
    final variance = multipliers
        .map((m) => pow(m - average, 2))
        .reduce((a, b) => a + b) / multipliers.length;
    
    final volatility = sqrt(variance) / average;
    return min(1.0, volatility); // Normaliser entre 0 et 1
  }

  // Analyser les patterns de timing
  static Map<String, dynamic> _analyzeTimingPatterns(List<GameResult> results) {
    if (results.length < 3) {
      return {'averageInterval': 15, 'patternStrength': 30.0, 'volatility': 0.5};
    }

    final intervals = <int>[];
    for (int i = 1; i < results.length; i++) {
      final timeDiff = results[i-1].timestamp.difference(results[i].timestamp).inSeconds.abs();
      if (timeDiff > 0 && timeDiff < 300) { // Filtrer les valeurs aberrantes
        intervals.add(timeDiff);
      }
    }

    if (intervals.isEmpty) {
      return {'averageInterval': 15, 'patternStrength': 30.0, 'volatility': 0.5};
    }

    final averageInterval = intervals.reduce((a, b) => a + b) / intervals.length;
    final variance = intervals
        .map((i) => pow(i - averageInterval, 2))
        .reduce((a, b) => a + b) / intervals.length;
    
    final patternStrength = max(20.0, 100.0 - sqrt(variance));
    final volatility = min(1.0, sqrt(variance) / averageInterval);

    return {
      'averageInterval': averageInterval.round(),
      'patternStrength': patternStrength,
      'volatility': volatility,
    };
  }

  // Extraire les features pour ML
  static Map<String, double> _extractFeatures(List<GameResult> history) {
    if (history.isEmpty) return {'volatility': 0.5, 'trend': 0.0, 'momentum': 0.0};

    final recent = history.take(10).toList();
    final multipliers = recent.map((r) => r.multiplier).toList();
    
    final volatility = _calculateVolatilityFromHistory(recent);
    final trend = multipliers.length > 1 ? 
        (multipliers.first - multipliers.last) / multipliers.length : 0.0;
    final momentum = multipliers.length > 2 ?
        (multipliers[0] - multipliers[1]) - (multipliers[1] - multipliers[2]) : 0.0;

    return {
      'volatility': volatility,
      'trend': trend,
      'momentum': momentum,
    };
  }

  // Simuler une régression ML
  static int _simulateMLRegression(Map<String, double> features) {
    final volatility = features['volatility'] ?? 0.5;
    final trend = features['trend'] ?? 0.0;
    final momentum = features['momentum'] ?? 0.0;

    // Simulation d'un modèle de régression
    final prediction = 20 + (volatility * 15) + (trend * 5) + (momentum * 3);
    return max(5, min(120, prediction.round()));
  }

  // Simuler la confiance ML
  static double _simulateMLConfidence(Map<String, double> features) {
    final volatility = features['volatility'] ?? 0.5;
    final baseConfidence = 85.0 - (volatility * 30);
    return max(40.0, min(95.0, baseConfidence));
  }
}

// Classe pour les résultats de prédiction de timing
class CrashTimingPrediction {
  final int predictedCrashTime; // En secondes
  final double predictedMultiplier;
  final double confidence; // 0-100
  final String method;
  final String riskLevel;
  final List<int> timeRange; // [min, max] en secondes
  final double volatilityIndex; // 0-1

  CrashTimingPrediction({
    required this.predictedCrashTime,
    required this.predictedMultiplier,
    required this.confidence,
    required this.method,
    required this.riskLevel,
    required this.timeRange,
    required this.volatilityIndex,
  });

  factory CrashTimingPrediction.empty() {
    return CrashTimingPrediction(
      predictedCrashTime: 15,
      predictedMultiplier: 2.0,
      confidence: 50.0,
      method: 'default',
      riskLevel: 'MEDIUM',
      timeRange: [10, 20],
      volatilityIndex: 0.5,
    );
  }

  String get formattedTime {
    final minutes = predictedCrashTime ~/ 60;
    final seconds = predictedCrashTime % 60;
    return minutes > 0 ? '${minutes}m ${seconds}s' : '${seconds}s';
  }

  String get formattedTimeRange {
    final minFormatted = timeRange[0] > 60 ? 
        '${timeRange[0] ~/ 60}m ${timeRange[0] % 60}s' : '${timeRange[0]}s';
    final maxFormatted = timeRange[1] > 60 ? 
        '${timeRange[1] ~/ 60}m ${timeRange[1] % 60}s' : '${timeRange[1]}s';
    return '$minFormatted - $maxFormatted';
  }

  Map<String, dynamic> toJson() {
    return {
      'predictedCrashTime': predictedCrashTime,
      'predictedMultiplier': predictedMultiplier,
      'confidence': confidence,
      'method': method,
      'riskLevel': riskLevel,
      'timeRange': timeRange,
      'volatilityIndex': volatilityIndex,
    };
  }
}
