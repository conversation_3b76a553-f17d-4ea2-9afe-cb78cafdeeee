import 'package:hive/hive.dart';

part 'game_result.g.dart';

@HiveType(typeId: 0)
class GameResult extends HiveObject {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final double multiplier;
  
  @HiveField(2)
  final DateTime timestamp;
  
  @HiveField(3)
  final double? betAmount;
  
  @HiveField(4)
  final bool isCrash;
  
  @HiveField(5)
  final double? profit;
  
  @HiveField(6)
  final String? strategy;

  @HiveField(7)
  final int? crashTimeSeconds; // Temps en secondes avant le crash

  @HiveField(8)
  final double? volatilityAtTime; // Volatilité au moment du résultat

  @HiveField(9)
  final String? gameSession; // ID de session de jeu

  @HiveField(10)
  final Map<String, dynamic>? metadata; // Données supplémentaires

  @HiveField(11)
  final bool isAutoSaved; // Indique si sauvegardé automatiquement

  @HiveField(12)
  final double? predictedMultiplier; // Multiplicateur prédit par l'IA

  @HiveField(13)
  final double? predictionAccuracy; // Précision de la prédiction

  GameResult({
    required this.id,
    required this.multiplier,
    required this.timestamp,
    this.betAmount,
    required this.isCrash,
    this.profit,
    this.strategy,
    this.crashTimeSeconds,
    this.volatilityAtTime,
    this.gameSession,
    this.metadata,
    this.isAutoSaved = true,
    this.predictedMultiplier,
    this.predictionAccuracy,
  });

  factory GameResult.create({
    required double multiplier,
    double? betAmount,
    String? strategy,
    int? crashTimeSeconds,
    double? volatilityAtTime,
    String? gameSession,
    Map<String, dynamic>? metadata,
    bool isAutoSaved = true,
    double? predictedMultiplier,
  }) {
    final isCrash = multiplier <= 1.1;
    final profit = betAmount != null ? (betAmount * multiplier - betAmount) : null;

    // Calculer la précision de la prédiction si disponible
    double? accuracy;
    if (predictedMultiplier != null) {
      final difference = (predictedMultiplier - multiplier).abs();
      accuracy = 100.0 - (difference / multiplier * 100.0);
      accuracy = accuracy.clamp(0.0, 100.0);
    }

    return GameResult(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      multiplier: multiplier,
      timestamp: DateTime.now(),
      betAmount: betAmount,
      isCrash: isCrash,
      profit: profit,
      strategy: strategy,
      crashTimeSeconds: crashTimeSeconds,
      volatilityAtTime: volatilityAtTime,
      gameSession: gameSession ?? DateTime.now().millisecondsSinceEpoch.toString(),
      metadata: metadata,
      isAutoSaved: isAutoSaved,
      predictedMultiplier: predictedMultiplier,
      predictionAccuracy: accuracy,
    );
  }

  GameResult copyWith({
    String? id,
    double? multiplier,
    DateTime? timestamp,
    double? betAmount,
    bool? isCrash,
    double? profit,
    String? strategy,
    int? crashTimeSeconds,
    double? volatilityAtTime,
    String? gameSession,
    Map<String, dynamic>? metadata,
    bool? isAutoSaved,
    double? predictedMultiplier,
    double? predictionAccuracy,
  }) {
    return GameResult(
      id: id ?? this.id,
      multiplier: multiplier ?? this.multiplier,
      timestamp: timestamp ?? this.timestamp,
      betAmount: betAmount ?? this.betAmount,
      isCrash: isCrash ?? this.isCrash,
      profit: profit ?? this.profit,
      strategy: strategy ?? this.strategy,
      crashTimeSeconds: crashTimeSeconds ?? this.crashTimeSeconds,
      volatilityAtTime: volatilityAtTime ?? this.volatilityAtTime,
      gameSession: gameSession ?? this.gameSession,
      metadata: metadata ?? this.metadata,
      isAutoSaved: isAutoSaved ?? this.isAutoSaved,
      predictedMultiplier: predictedMultiplier ?? this.predictedMultiplier,
      predictionAccuracy: predictionAccuracy ?? this.predictionAccuracy,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'multiplier': multiplier,
      'timestamp': timestamp.toIso8601String(),
      'betAmount': betAmount,
      'isCrash': isCrash,
      'profit': profit,
      'strategy': strategy,
      'crashTimeSeconds': crashTimeSeconds,
      'volatilityAtTime': volatilityAtTime,
      'gameSession': gameSession,
      'metadata': metadata,
      'isAutoSaved': isAutoSaved,
      'predictedMultiplier': predictedMultiplier,
      'predictionAccuracy': predictionAccuracy,
    };
  }

  factory GameResult.fromJson(Map<String, dynamic> json) {
    return GameResult(
      id: json['id'],
      multiplier: json['multiplier'].toDouble(),
      timestamp: DateTime.parse(json['timestamp']),
      betAmount: json['betAmount']?.toDouble(),
      isCrash: json['isCrash'],
      profit: json['profit']?.toDouble(),
      strategy: json['strategy'],
      crashTimeSeconds: json['crashTimeSeconds']?.toInt(),
      volatilityAtTime: json['volatilityAtTime']?.toDouble(),
      gameSession: json['gameSession'],
      metadata: json['metadata'] as Map<String, dynamic>?,
      isAutoSaved: json['isAutoSaved'] ?? true,
      predictedMultiplier: json['predictedMultiplier']?.toDouble(),
      predictionAccuracy: json['predictionAccuracy']?.toDouble(),
    );
  }

  // Méthodes utilitaires pour l'analyse
  bool get isHighMultiplier => multiplier >= 5.0;
  bool get isMediumMultiplier => multiplier >= 2.0 && multiplier < 5.0;
  bool get isLowMultiplier => multiplier < 2.0;
  bool get isExtremeMultiplier => multiplier >= 10.0;

  String get riskLevel {
    if (multiplier >= 10.0) return 'EXTREME';
    if (multiplier >= 5.0) return 'HIGH';
    if (multiplier >= 2.0) return 'MEDIUM';
    return 'LOW';
  }

  Duration get timeSinceResult => DateTime.now().difference(timestamp);
  bool get isRecent => timeSinceResult.inMinutes < 30;
  bool get isVeryRecent => timeSinceResult.inMinutes < 5;

  // Analyse de la précision de prédiction
  bool get wasPredictionAccurate => predictionAccuracy != null && predictionAccuracy! >= 70.0;
  bool get wasPredictionVeryAccurate => predictionAccuracy != null && predictionAccuracy! >= 85.0;

  // Analyse du temps de crash
  bool get wasQuickCrash => crashTimeSeconds != null && crashTimeSeconds! < 10;
  bool get wasSlowCrash => crashTimeSeconds != null && crashTimeSeconds! > 30;

  // Analyse de volatilité
  bool get wasHighVolatility => volatilityAtTime != null && volatilityAtTime! > 0.7;
  bool get wasLowVolatility => volatilityAtTime != null && volatilityAtTime! < 0.3;

  // Méthode pour calculer la probabilité d'apparition de ce multiplicateur
  static double calculateMultiplierProbability(double targetMultiplier, List<GameResult> history) {
    if (history.isEmpty) return 0.0;

    final tolerance = 0.1; // Tolérance de ±0.1
    final matchingResults = history.where((result) =>
      (result.multiplier - targetMultiplier).abs() <= tolerance
    ).length;

    return (matchingResults / history.length) * 100.0;
  }

  // Méthode pour prédire le temps de crash basé sur l'historique
  static int? predictCrashTime(List<GameResult> history, double targetMultiplier) {
    if (history.isEmpty) return null;

    final similarResults = history.where((result) =>
      (result.multiplier - targetMultiplier).abs() <= 0.5 &&
      result.crashTimeSeconds != null
    ).toList();

    if (similarResults.isEmpty) return null;

    final averageTime = similarResults
        .map((r) => r.crashTimeSeconds!)
        .reduce((a, b) => a + b) / similarResults.length;

    return averageTime.round();
  }

  @override
  String toString() {
    return 'GameResult(id: $id, multiplier: $multiplier, timestamp: $timestamp, isCrash: $isCrash, crashTime: ${crashTimeSeconds}s, accuracy: ${predictionAccuracy?.toStringAsFixed(1)}%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GameResult && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
