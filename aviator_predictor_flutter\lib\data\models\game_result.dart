import 'package:hive/hive.dart';

part 'game_result.g.dart';

@HiveType(typeId: 0)
class GameResult extends HiveObject {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final double multiplier;
  
  @HiveField(2)
  final DateTime timestamp;
  
  @HiveField(3)
  final double? betAmount;
  
  @HiveField(4)
  final bool isCrash;
  
  @HiveField(5)
  final double? profit;
  
  @HiveField(6)
  final String? strategy;

  GameResult({
    required this.id,
    required this.multiplier,
    required this.timestamp,
    this.betAmount,
    required this.isCrash,
    this.profit,
    this.strategy,
  });

  factory GameResult.create({
    required double multiplier,
    double? betAmount,
    String? strategy,
  }) {
    final isCrash = multiplier <= 1.1;
    final profit = betAmount != null ? (betAmount * multiplier - betAmount) : null;
    
    return GameResult(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      multiplier: multiplier,
      timestamp: DateTime.now(),
      betAmount: betAmount,
      isCrash: isCrash,
      profit: profit,
      strategy: strategy,
    );
  }

  GameResult copyWith({
    String? id,
    double? multiplier,
    DateTime? timestamp,
    double? betAmount,
    bool? isCrash,
    double? profit,
    String? strategy,
  }) {
    return GameResult(
      id: id ?? this.id,
      multiplier: multiplier ?? this.multiplier,
      timestamp: timestamp ?? this.timestamp,
      betAmount: betAmount ?? this.betAmount,
      isCrash: isCrash ?? this.isCrash,
      profit: profit ?? this.profit,
      strategy: strategy ?? this.strategy,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'multiplier': multiplier,
      'timestamp': timestamp.toIso8601String(),
      'betAmount': betAmount,
      'isCrash': isCrash,
      'profit': profit,
      'strategy': strategy,
    };
  }

  factory GameResult.fromJson(Map<String, dynamic> json) {
    return GameResult(
      id: json['id'],
      multiplier: json['multiplier'].toDouble(),
      timestamp: DateTime.parse(json['timestamp']),
      betAmount: json['betAmount']?.toDouble(),
      isCrash: json['isCrash'],
      profit: json['profit']?.toDouble(),
      strategy: json['strategy'],
    );
  }

  @override
  String toString() {
    return 'GameResult(id: $id, multiplier: $multiplier, timestamp: $timestamp, isCrash: $isCrash)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GameResult && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
