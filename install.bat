@echo off
echo ========================================
echo    Aviator Predictor Pro - Installation
echo ========================================
echo.

echo Verification de Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Python n'est pas installe ou pas dans le PATH
    echo Veuillez installer Python depuis https://python.org
    pause
    exit /b 1
)

echo Python detecte!
echo.

echo Installation des dependances...
echo.

echo Installation de numpy...
pip install numpy

echo Installation de pandas...
pip install pandas

echo Installation de matplotlib...
pip install matplotlib

echo Installation de scikit-learn...
pip install scikit-learn

echo.
echo ========================================
echo Installation terminee!
echo ========================================
echo.

echo Pour lancer Aviator Predictor Pro:
echo   Interface graphique: python prev.py
echo   Tests: python test_aviator.py
echo.

echo Appuyez sur une touche pour lancer l'application...
pause >nul

python prev.py
