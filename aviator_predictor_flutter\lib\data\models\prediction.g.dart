// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'prediction.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PredictionAdapter extends TypeAdapter<Prediction> {
  @override
  final int typeId = 1;

  @override
  Prediction read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Prediction(
      id: fields[0] as String,
      predictedMultiplier: fields[1] as double,
      crashProbability: fields[2] as double,
      timestamp: fields[3] as DateTime,
      riskLevel: fields[4] as String,
      confidence: fields[5] as double,
      suggestion: fields[6] as String,
      method: fields[7] as String,
      modelPredictions: (fields[8] as Map).cast<String, double>(),
      actualMultiplier: fields[9] as double?,
      wasCorrect: fields[10] as bool?,
      volatilityIndex: fields[11] as double,
      trendMomentum: fields[12] as double,
    );
  }

  @override
  void write(BinaryWriter writer, Prediction obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.predictedMultiplier)
      ..writeByte(2)
      ..write(obj.crashProbability)
      ..writeByte(3)
      ..write(obj.timestamp)
      ..writeByte(4)
      ..write(obj.riskLevel)
      ..writeByte(5)
      ..write(obj.confidence)
      ..writeByte(6)
      ..write(obj.suggestion)
      ..writeByte(7)
      ..write(obj.method)
      ..writeByte(8)
      ..write(obj.modelPredictions)
      ..writeByte(9)
      ..write(obj.actualMultiplier)
      ..writeByte(10)
      ..write(obj.wasCorrect)
      ..writeByte(11)
      ..write(obj.volatilityIndex)
      ..writeByte(12)
      ..write(obj.trendMomentum);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PredictionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
