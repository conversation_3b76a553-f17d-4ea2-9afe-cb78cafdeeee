#!/usr/bin/env python3
"""
Test script pour démontrer les nouvelles fonctionnalités d'Aviator Predictor Pro
"""

import sys
import time
import random
import numpy as np
from prev import AdvancedAviatorPredictor

def generate_realistic_multipliers(count=100):
    """Génère des multiplicateurs réalistes pour les tests"""
    multipliers = []
    
    for i in range(count):
        # Simulation réaliste basée sur les patterns d'Aviator
        rand = random.random()
        
        if rand < 0.15:  # 15% de chance de crash très bas
            mult = round(random.uniform(1.0, 1.1), 2)
        elif rand < 0.35:  # 20% de chance de multiplicateur bas
            mult = round(random.uniform(1.1, 1.5), 2)
        elif rand < 0.65:  # 30% de chance de multiplicateur moyen
            mult = round(random.uniform(1.5, 3.0), 2)
        elif rand < 0.85:  # 20% de chance de multiplicateur élevé
            mult = round(random.uniform(3.0, 8.0), 2)
        else:  # 15% de chance de très haut multiplicateur
            mult = round(random.uniform(8.0, 50.0), 2)
            
        multipliers.append(mult)
    
    return multipliers

def test_crash_prediction_accuracy():
    """Test de précision de prédiction des crashs"""
    print("🧪 Test de précision de prédiction des crashs")
    print("=" * 50)
    
    predictor = AdvancedAviatorPredictor()
    
    # Génération de données de test
    test_data = generate_realistic_multipliers(200)
    
    # Entraînement avec les 150 premiers
    training_data = test_data[:150]
    for mult in training_data:
        predictor.add_game_result(mult)
    
    print(f"✅ Modèle entraîné avec {len(training_data)} parties")
    
    # Test avec les 50 derniers
    test_data_subset = test_data[150:]
    correct_predictions = 0
    crash_predictions = 0
    actual_crashes = 0
    
    for i, actual_mult in enumerate(test_data_subset):
        # Prédiction
        pred = predictor.predict_next('advanced_hybrid')
        
        if isinstance(pred, dict):
            predicted_crash = pred['crash_probability'] > 0.5
            actual_crash = actual_mult <= 1.1
            
            if predicted_crash:
                crash_predictions += 1
            if actual_crash:
                actual_crashes += 1
            if predicted_crash == actual_crash:
                correct_predictions += 1
            
            print(f"Partie {i+1}: Prédit={pred['prediction']:.2f}x (crash: {pred['crash_probability']:.1%}) | "
                  f"Réel={actual_mult:.2f}x {'💥' if actual_crash else '✅'}")
        
        # Ajouter le résultat réel
        predictor.add_game_result(actual_mult)
    
    # Résultats
    accuracy = correct_predictions / len(test_data_subset) * 100
    print(f"\n📊 Résultats du test:")
    print(f"Précision globale: {accuracy:.1f}%")
    print(f"Crashs prédits: {crash_predictions}")
    print(f"Crashs réels: {actual_crashes}")
    print(f"Prédictions correctes: {correct_predictions}/{len(test_data_subset)}")

def test_volatility_analysis():
    """Test d'analyse de volatilité"""
    print("\n🌊 Test d'analyse de volatilité")
    print("=" * 50)
    
    predictor = AdvancedAviatorPredictor()
    
    # Scénario 1: Faible volatilité
    stable_data = [1.2, 1.3, 1.4, 1.3, 1.2, 1.4, 1.3, 1.2, 1.3, 1.4] * 5
    for mult in stable_data:
        predictor.add_game_result(mult)
    
    print(f"Volatilité (données stables): {predictor.volatility_index:.3f}")
    
    # Scénario 2: Haute volatilité
    predictor = AdvancedAviatorPredictor()
    volatile_data = [1.0, 15.2, 1.1, 8.5, 1.0, 25.0, 1.2, 12.3, 1.0, 18.7] * 3
    for mult in volatile_data:
        predictor.add_game_result(mult)
    
    print(f"Volatilité (données volatiles): {predictor.volatility_index:.3f}")

def test_pattern_recognition():
    """Test de reconnaissance de motifs"""
    print("\n🔍 Test de reconnaissance de motifs")
    print("=" * 50)
    
    predictor = AdvancedAviatorPredictor()
    
    # Création d'un motif répétitif
    pattern = [1.2, 1.5, 2.0, 1.1, 3.5]
    data = pattern * 20  # Répéter le motif 20 fois
    
    for mult in data:
        predictor.add_game_result(mult)
    
    # Analyse des motifs
    if predictor.patterns_db:
        print("Motifs détectés:")
        for seq_len, data in predictor.patterns_db.items():
            if data['most_common']:
                print(f"Séquences de {seq_len}: {data['most_common'][:3]}")

def demo_real_time_features():
    """Démonstration des fonctionnalités temps réel"""
    print("\n⚡ Démonstration temps réel")
    print("=" * 50)
    
    predictor = AdvancedAviatorPredictor()
    
    # Simulation d'une session de jeu
    session_data = generate_realistic_multipliers(30)
    
    for i, mult in enumerate(session_data):
        predictor.add_game_result(mult)
        
        if i >= 10 and i % 5 == 0:  # Prédiction tous les 5 tours après 10 tours
            pred = predictor.predict_next('advanced_hybrid')
            if isinstance(pred, dict):
                print(f"\nTour {i+1}:")
                print(f"  Prédiction: {pred['prediction']:.2f}x")
                print(f"  Probabilité crash: {pred['crash_probability']:.1%}")
                print(f"  Niveau de risque: {pred['risk_level']}")
                print(f"  Suggestion: {pred['suggestion'][:50]}...")
    
    # Rapport final
    print(f"\n📈 Rapport de session:")
    print(predictor.session_report())

def main():
    """Fonction principale de test"""
    print("🛩️ AVIATOR PREDICTOR PRO - TESTS AVANCÉS")
    print("=" * 60)
    
    tests = [
        ("Précision de prédiction des crashs", test_crash_prediction_accuracy),
        ("Analyse de volatilité", test_volatility_analysis),
        ("Reconnaissance de motifs", test_pattern_recognition),
        ("Fonctionnalités temps réel", demo_real_time_features)
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n🚀 Lancement: {test_name}")
            test_func()
            print(f"✅ {test_name} terminé avec succès")
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
        
        print("\n" + "="*60)
    
    print("🎉 Tous les tests terminés!")
    print("\nPour lancer l'interface graphique complète:")
    print("python prev.py")

if __name__ == "__main__":
    main()
