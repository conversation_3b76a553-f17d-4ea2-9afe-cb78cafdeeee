{"thresholds": {"crash_threshold": 1.1, "low_threshold": 1.5, "medium_threshold": 5.0, "high_risk_threshold": 10.0}, "model_settings": {"window_size": 15, "sequence_length": 5, "retrain_frequency": 15, "min_data_for_training": 30}, "prediction_weights": {"polynomial": 0.2, "random_forest": 0.4, "neural_network": 0.4}, "gui_settings": {"auto_predict": false, "theme": "dark", "update_frequency": 1000}, "alerts": {"high_crash_probability": 0.7, "moderate_crash_probability": 0.5, "volatility_threshold": 0.5}}