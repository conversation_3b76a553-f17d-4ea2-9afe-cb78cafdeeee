@echo off
echo ========================================
echo    AVIATOR PREDICTOR PRO - BUILD SCRIPT
echo ========================================
echo.

:: Vérifier que Flutter est installé
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Flutter n'est pas installé ou n'est pas dans le PATH
    echo Veuillez installer Flutter: https://flutter.dev/docs/get-started/install
    pause
    exit /b 1
)

echo [1/6] Vérification de l'environnement Flutter...
flutter doctor --android-licenses >nul 2>&1

echo [2/6] Nettoyage des builds précédents...
flutter clean
flutter pub get

echo [3/6] Compilation pour Windows Desktop...
flutter build windows --release
if %errorlevel% neq 0 (
    echo ERREUR: Échec de la compilation Windows
    pause
    exit /b 1
)

echo [4/6] Compilation pour Web...
flutter build web --release --web-renderer html
if %errorlevel% neq 0 (
    echo ERREUR: Échec de la compilation Web
    pause
    exit /b 1
)

echo [5/6] Compilation pour Android APK...
flutter build apk --release --split-per-abi
if %errorlevel% neq 0 (
    echo ERREUR: Échec de la compilation Android
    pause
    exit /b 1
)

echo [6/6] Création du package de distribution...

:: Créer le dossier de distribution
if exist "dist" rmdir /s /q "dist"
mkdir "dist"
mkdir "dist\windows"
mkdir "dist\web"
mkdir "dist\android"

:: Copier les builds
echo Copie des fichiers Windows...
xcopy "build\windows\x64\runner\Release\*" "dist\windows\" /E /I /H /Y

echo Copie des fichiers Web...
xcopy "build\web\*" "dist\web\" /E /I /H /Y

echo Copie des fichiers Android...
copy "build\app\outputs\flutter-apk\*.apk" "dist\android\"

:: Créer un fichier README
echo Création du fichier README...
(
echo AVIATOR PREDICTOR PRO - DISTRIBUTION
echo ====================================
echo.
echo Ce package contient les versions compilées de l'application Aviator Predictor Pro
echo pour différentes plateformes.
echo.
echo CONTENU:
echo --------
echo.
echo 1. WINDOWS ^(dist/windows/^)
echo    - Exécutable: aviator_predictor_flutter.exe
echo    - Fichiers de support: data/, flutter_windows.dll, etc.
echo    - Configuration requise: Windows 10 ou plus récent
echo.
echo 2. WEB ^(dist/web/^)
echo    - Fichiers web: index.html, main.dart.js, etc.
echo    - Déployez ces fichiers sur un serveur web
echo    - Compatible avec tous les navigateurs modernes
echo.
echo 3. ANDROID ^(dist/android/^)
echo    - Fichiers APK pour différentes architectures
echo    - app-arm64-v8a-release.apk ^(recommandé pour la plupart des appareils^)
echo    - app-armeabi-v7a-release.apk ^(appareils plus anciens^)
echo    - app-x86_64-release.apk ^(émulateurs^)
echo.
echo INSTALLATION:
echo -------------
echo.
echo WINDOWS:
echo 1. Extraire le contenu de dist/windows/ dans un dossier
echo 2. Exécuter aviator_predictor_flutter.exe
echo.
echo WEB:
echo 1. Copier le contenu de dist/web/ sur votre serveur web
echo 2. Accéder à index.html via votre navigateur
echo.
echo ANDROID:
echo 1. Transférer le fichier APK approprié sur votre appareil
echo 2. Activer "Sources inconnues" dans les paramètres
echo 3. Installer l'APK
echo.
echo FONCTIONNALITÉS:
echo ----------------
echo.
echo ✅ Prédictions avancées avec IA ^(LSTM, Ensemble, RL, Génétique, Quantique^)
echo ✅ Sauvegarde automatique de l'historique
echo ✅ Prédiction du timing de crash
echo ✅ Analyse des probabilités de multiplicateurs
echo ✅ Interface moderne Material Design 3
echo ✅ Graphiques interactifs en temps réel
echo ✅ Mode sombre/clair
echo ✅ Responsive design ^(desktop, tablet, mobile^)
echo ✅ Export/Import des données
echo ✅ Alertes intelligentes
echo ✅ Statistiques avancées
echo.
echo SUPPORT:
echo --------
echo.
echo Pour toute question ou problème, veuillez consulter la documentation
echo ou contacter le support technique.
echo.
echo Version: 2.0.0
echo Date de build: %date% %time%
echo.
) > "dist\README.txt"

:: Créer un script de lancement Windows
echo Création du script de lancement...
(
echo @echo off
echo title Aviator Predictor Pro
echo echo Démarrage d'Aviator Predictor Pro...
echo cd /d "%%~dp0"
echo start "" "aviator_predictor_flutter.exe"
echo echo Application lancée!
echo timeout /t 2 >nul
) > "dist\windows\Lancer_Aviator_Predictor.bat"

:: Créer un fichier de version
(
echo {
echo   "name": "Aviator Predictor Pro",
echo   "version": "2.0.0",
echo   "build_date": "%date% %time%",
echo   "platforms": [
echo     "Windows x64",
echo     "Web ^(HTML^)",
echo     "Android ^(ARM64, ARMv7, x86_64^)"
echo   ],
echo   "features": [
echo     "Advanced AI Predictions",
echo     "Automatic History Saving",
echo     "Crash Timing Prediction",
echo     "Multiplier Probability Analysis",
echo     "Interactive Charts",
echo     "Material Design 3",
echo     "Dark/Light Theme",
echo     "Responsive Design",
echo     "Data Export/Import",
echo     "Smart Alerts"
echo   ]
echo }
) > "dist\version.json"

echo.
echo ========================================
echo           BUILD TERMINÉ AVEC SUCCÈS!
echo ========================================
echo.
echo Les applications ont été compilées et sont disponibles dans le dossier 'dist':
echo.
echo 📁 dist/
echo   ├── 🖥️  windows/          ^(Application Windows^)
echo   ├── 🌐 web/              ^(Application Web^)
echo   ├── 📱 android/          ^(Applications Android APK^)
echo   ├── 📄 README.txt        ^(Instructions d'installation^)
echo   └── 📋 version.json      ^(Informations de version^)
echo.
echo TAILLES APPROXIMATIVES:
dir "dist\windows" | find "aviator_predictor_flutter.exe"
echo Web: ~10-15 MB
dir "dist\android" | find ".apk"
echo.
echo Pour tester:
echo - Windows: Exécuter dist\windows\aviator_predictor_flutter.exe
echo - Web: Ouvrir dist\web\index.html dans un navigateur
echo - Android: Installer l'APK approprié sur votre appareil
echo.
echo ✅ Toutes les plateformes ont été compilées avec succès!
echo.
pause
