import 'package:hive/hive.dart';

part 'prediction.g.dart';

@HiveType(typeId: 1)
class Prediction extends HiveObject {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final double predictedMultiplier;
  
  @HiveField(2)
  final double crashProbability;
  
  @HiveField(3)
  final DateTime timestamp;
  
  @HiveField(4)
  final String riskLevel;
  
  @HiveField(5)
  final double confidence;
  
  @HiveField(6)
  final String suggestion;
  
  @HiveField(7)
  final String method;
  
  @HiveField(8)
  final Map<String, double> modelPredictions;
  
  @HiveField(9)
  final double? actualMultiplier;
  
  @HiveField(10)
  final bool? wasCorrect;
  
  @HiveField(11)
  final double volatilityIndex;
  
  @HiveField(12)
  final double trendMomentum;

  Prediction({
    required this.id,
    required this.predictedMultiplier,
    required this.crashProbability,
    required this.timestamp,
    required this.riskLevel,
    required this.confidence,
    required this.suggestion,
    required this.method,
    required this.modelPredictions,
    this.actualMultiplier,
    this.wasCorrect,
    required this.volatilityIndex,
    required this.trendMomentum,
  });

  factory Prediction.create({
    required double predictedMultiplier,
    required double crashProbability,
    required String riskLevel,
    required double confidence,
    required String suggestion,
    required String method,
    required Map<String, double> modelPredictions,
    required double volatilityIndex,
    required double trendMomentum,
  }) {
    return Prediction(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      predictedMultiplier: predictedMultiplier,
      crashProbability: crashProbability,
      timestamp: DateTime.now(),
      riskLevel: riskLevel,
      confidence: confidence,
      suggestion: suggestion,
      method: method,
      modelPredictions: modelPredictions,
      volatilityIndex: volatilityIndex,
      trendMomentum: trendMomentum,
    );
  }

  Prediction copyWith({
    String? id,
    double? predictedMultiplier,
    double? crashProbability,
    DateTime? timestamp,
    String? riskLevel,
    double? confidence,
    String? suggestion,
    String? method,
    Map<String, double>? modelPredictions,
    double? actualMultiplier,
    bool? wasCorrect,
    double? volatilityIndex,
    double? trendMomentum,
  }) {
    return Prediction(
      id: id ?? this.id,
      predictedMultiplier: predictedMultiplier ?? this.predictedMultiplier,
      crashProbability: crashProbability ?? this.crashProbability,
      timestamp: timestamp ?? this.timestamp,
      riskLevel: riskLevel ?? this.riskLevel,
      confidence: confidence ?? this.confidence,
      suggestion: suggestion ?? this.suggestion,
      method: method ?? this.method,
      modelPredictions: modelPredictions ?? this.modelPredictions,
      actualMultiplier: actualMultiplier ?? this.actualMultiplier,
      wasCorrect: wasCorrect ?? this.wasCorrect,
      volatilityIndex: volatilityIndex ?? this.volatilityIndex,
      trendMomentum: trendMomentum ?? this.trendMomentum,
    );
  }

  // Evaluate prediction accuracy when actual result is known
  Prediction evaluate(double actualResult) {
    final crashThreshold = 1.1;
    final predictedCrash = crashProbability > 0.5;
    final actualCrash = actualResult <= crashThreshold;
    
    // Calculate if prediction was correct
    final predictionError = (predictedMultiplier - actualResult).abs();
    final isAccurate = predictionError <= (predictedMultiplier * 0.2); // 20% tolerance
    final crashPredictionCorrect = predictedCrash == actualCrash;
    
    final wasCorrect = isAccurate && crashPredictionCorrect;
    
    return copyWith(
      actualMultiplier: actualResult,
      wasCorrect: wasCorrect,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'predictedMultiplier': predictedMultiplier,
      'crashProbability': crashProbability,
      'timestamp': timestamp.toIso8601String(),
      'riskLevel': riskLevel,
      'confidence': confidence,
      'suggestion': suggestion,
      'method': method,
      'modelPredictions': modelPredictions,
      'actualMultiplier': actualMultiplier,
      'wasCorrect': wasCorrect,
      'volatilityIndex': volatilityIndex,
      'trendMomentum': trendMomentum,
    };
  }

  factory Prediction.fromJson(Map<String, dynamic> json) {
    return Prediction(
      id: json['id'],
      predictedMultiplier: json['predictedMultiplier'].toDouble(),
      crashProbability: json['crashProbability'].toDouble(),
      timestamp: DateTime.parse(json['timestamp']),
      riskLevel: json['riskLevel'],
      confidence: json['confidence'].toDouble(),
      suggestion: json['suggestion'],
      method: json['method'],
      modelPredictions: Map<String, double>.from(json['modelPredictions']),
      actualMultiplier: json['actualMultiplier']?.toDouble(),
      wasCorrect: json['wasCorrect'],
      volatilityIndex: json['volatilityIndex'].toDouble(),
      trendMomentum: json['trendMomentum'].toDouble(),
    );
  }

  @override
  String toString() {
    return 'Prediction(id: $id, predicted: $predictedMultiplier, crash: ${(crashProbability * 100).toStringAsFixed(1)}%)';
  }
}
