<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Aviator Predictor Pro - Advanced AI-powered crash prediction for Aviator game">
  <meta name="keywords" content="aviator, predictor, AI, machine learning, crash prediction, gambling, analytics">
  <meta name="author" content="Aviator Predictor Pro Team">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Aviator Predictor Pro">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <!-- PWA -->
  <link rel="manifest" href="manifest.json">

  <!-- Open Graph meta tags -->
  <meta property="og:title" content="Aviator Predictor Pro">
  <meta property="og:description" content="Advanced AI-powered crash prediction for Aviator game">
  <meta property="og:image" content="icons/Icon-512.png">
  <meta property="og:url" content="https://aviatorpredictor.pro">
  <meta property="og:type" content="website">

  <!-- Twitter Card meta tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Aviator Predictor Pro">
  <meta name="twitter:description" content="Advanced AI-powered crash prediction for Aviator game">
  <meta name="twitter:image" content="icons/Icon-512.png">

  <title>Aviator Predictor Pro</title>

  <!-- Responsive viewport -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

  <!-- Theme color -->
  <meta name="theme-color" content="#00FF88">

  <!-- Preload critical resources -->
  <link rel="preload" href="assets/fonts/Roboto-Regular.ttf" as="font" type="font/ttf" crossorigin>
  <link rel="preload" href="assets/fonts/Roboto-Bold.ttf" as="font" type="font/ttf" crossorigin>

  <!-- Custom CSS for loading screen -->
  <style>
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
      font-family: 'Roboto', sans-serif;
      overflow: hidden;
    }

    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
      z-index: 9999;
    }

    .loading-logo {
      width: 120px;
      height: 120px;
      background: linear-gradient(135deg, #00FF88 0%, #00D4FF 100%);
      border-radius: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 30px;
      animation: pulse 2s ease-in-out infinite;
    }

    .loading-logo::before {
      content: "✈️";
      font-size: 48px;
      animation: rotate 3s linear infinite;
    }

    .loading-title {
      color: #ffffff;
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 10px;
      text-align: center;
    }

    .loading-subtitle {
      color: #00FF88;
      font-size: 16px;
      margin-bottom: 40px;
      text-align: center;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 3px solid rgba(0, 255, 136, 0.3);
      border-top: 3px solid #00FF88;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-progress {
      width: 200px;
      height: 4px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 2px;
      margin-top: 20px;
      overflow: hidden;
    }

    .loading-progress-bar {
      height: 100%;
      background: linear-gradient(90deg, #00FF88 0%, #00D4FF 100%);
      border-radius: 2px;
      animation: progress 3s ease-in-out infinite;
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    @keyframes rotate {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    @keyframes progress {
      0% { width: 0%; }
      50% { width: 70%; }
      100% { width: 100%; }
    }

    .fade-out {
      opacity: 0;
      transition: opacity 0.5s ease-out;
    }

    /* Hide loading screen when Flutter is ready */
    .flutter-ready .loading-container {
      display: none;
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div class="loading-container" id="loading">
    <div class="loading-logo"></div>
    <div class="loading-title">Aviator Predictor Pro</div>
    <div class="loading-subtitle">Chargement de l'IA avancée...</div>
    <div class="loading-spinner"></div>
    <div class="loading-progress">
      <div class="loading-progress-bar"></div>
    </div>
  </div>

  <!-- Flutter app will be inserted here -->
  <div id="app"></div>

  <!-- Service Worker registration -->
  <script>
    window.addEventListener('load', function(ev) {
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            // Hide loading screen
            setTimeout(() => {
              const loading = document.getElementById('loading');
              if (loading) {
                loading.classList.add('fade-out');
                setTimeout(() => {
                  loading.style.display = 'none';
                  document.body.classList.add('flutter-ready');
                }, 500);
              }
            }, 1000);
            
            appRunner.runApp();
          });
        }
      });
    });

    // PWA install prompt
    let deferredPrompt;
    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault();
      deferredPrompt = e;
      
      // Show install button or banner
      console.log('PWA install prompt available');
    });

    // Track PWA install
    window.addEventListener('appinstalled', (evt) => {
      console.log('PWA was installed');
    });

    // Handle offline/online status
    window.addEventListener('online', () => {
      console.log('App is online');
    });

    window.addEventListener('offline', () => {
      console.log('App is offline');
    });
  </script>

  <!-- Flutter Web Bootstrap -->
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
