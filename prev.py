import re
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter, deque
from datetime import datetime, timedelta
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.preprocessing import PolynomialFeatures, StandardScaler
from sklearn.pipeline import make_pipeline
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import accuracy_score, mean_squared_error
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import json
import warnings
warnings.filterwarnings('ignore')

class AdvancedAviatorPredictor:
    def __init__(self, window_size=15, sequence_length=5):
        self.multipliers = []
        self.timestamps = []
        self.bets = []
        self.window_size = window_size
        self.sequence_length = sequence_length
        self.session_data = []
        self.models = {}
        self.patterns_db = {}
        self.crash_predictor = None
        self.scaler = StandardScaler()

        # Configuration des seuils améliorés
        self.CRASH_THRESHOLD = 1.1  # Seuil pour considérer un crash
        self.LOW_THRESHOLD = 1.5
        self.MEDIUM_THRESHOLD = 5.0
        self.HIGH_RISK_THRESHOLD = 10.0

        # Nouvelles métriques pour l'analyse des crashs
        self.crash_patterns = []
        self.volatility_index = 0
        self.trend_momentum = 0
        self.risk_score = 0

        # Historique des prédictions pour évaluer la précision
        self.prediction_history = []
        self.accuracy_metrics = {
            'correct_predictions': 0,
            'total_predictions': 0,
            'crash_predictions': 0,
            'actual_crashes': 0
        }
        
    def load_data(self, text):
        """Charge les données à partir du texte brut avec une extraction améliorée"""
        # Extraction des multiplicateurs avec timestamp
        pattern = r"(\d{2}:\d{2}).*?(\d+\.\d+)x"
        matches = re.findall(pattern, text)

        for time, mult in matches:
            self.timestamps.append(time)
            self.multipliers.append(float(mult))

        # Extraction des mises avec une regex plus robuste
        bet_pattern = r"Miser Auto[^\d]*(\d{1,3}(?:,\d{3})*)"
        self.bets = [int(b.replace(',', '')) for b in re.findall(bet_pattern, text)]

        # Construction du DataFrame pour analyse
        self.df = pd.DataFrame({
            'timestamp': self.timestamps,
            'multiplier': self.multipliers
        })

        # Conversion des timestamps en datetime
        try:
            self.df['datetime'] = pd.to_datetime(self.df['timestamp'], format='%H:%M')
        except:
            self.df['datetime'] = pd.to_datetime(self.df['timestamp'], errors='coerce')

        # Ajout de nouvelles colonnes d'analyse
        self.df['is_crash'] = self.df['multiplier'] <= self.CRASH_THRESHOLD
        self.df['volatility'] = self.df['multiplier'].rolling(window=5).std()
        self.df['trend'] = self.df['multiplier'].rolling(window=3).mean().diff()

        print(f"Données chargées: {len(self.multipliers)} parties avec {len(self.bets)} mises enregistrées")

        # Analyse des crashs
        self.analyze_crash_patterns()

        # Entraînement des modèles
        self.train_prediction_models()

        # Construction de la base de données de motifs
        self.build_pattern_database()
    
    def analyze_crash_patterns(self):
        """Analyse approfondie des patterns de crash"""
        if len(self.multipliers) < 10:
            return

        crashes = [i for i, m in enumerate(self.multipliers) if m <= self.CRASH_THRESHOLD]

        # Analyse des intervalles entre crashs
        if len(crashes) > 1:
            intervals = [crashes[i+1] - crashes[i] for i in range(len(crashes)-1)]
            self.crash_patterns = {
                'avg_interval': np.mean(intervals),
                'std_interval': np.std(intervals),
                'min_interval': min(intervals),
                'max_interval': max(intervals),
                'crash_rate': len(crashes) / len(self.multipliers),
                'last_crash_distance': len(self.multipliers) - crashes[-1] if crashes else 0
            }

        # Calcul de l'indice de volatilité
        if len(self.multipliers) >= 10:
            recent_mults = self.multipliers[-10:]
            self.volatility_index = np.std(recent_mults) / np.mean(recent_mults)

        # Calcul du momentum de tendance
        if len(self.multipliers) >= 5:
            recent_trend = np.diff(self.multipliers[-5:])
            self.trend_momentum = np.mean(recent_trend)

    def train_prediction_models(self):
        """Entraîne plusieurs modèles de prédiction avancés"""
        if len(self.multipliers) < 30:
            print("Attention: Peu de données disponibles pour des modèles fiables")
            return

        # Préparation des features
        X, y = self.prepare_features()

        if len(X) < 10:
            return

        # Modèle polynomial (existant)
        self.models['polynomial'] = make_pipeline(
            PolynomialFeatures(degree=2),
            LinearRegression()
        )

        # Modèle Random Forest pour capturer les non-linéarités
        self.models['random_forest'] = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42
        )

        # Modèle de réseau de neurones
        self.models['neural_network'] = MLPRegressor(
            hidden_layer_sizes=(50, 25),
            max_iter=1000,
            random_state=42
        )

        # Modèle de prédiction de crash (classification)
        y_crash = (y <= self.CRASH_THRESHOLD).astype(int)
        self.crash_predictor = LogisticRegression(random_state=42)

        # Entraînement des modèles
        try:
            for name, model in self.models.items():
                model.fit(X, y)

            if len(np.unique(y_crash)) > 1:  # Vérifier qu'il y a des crashs et des non-crashs
                self.crash_predictor.fit(X, y_crash)
        except Exception as e:
            print(f"Erreur lors de l'entraînement des modèles: {e}")

    def prepare_features(self):
        """Prépare les features pour l'entraînement des modèles"""
        if len(self.multipliers) < 10:
            return np.array([]), np.array([])

        features = []
        targets = []

        for i in range(5, len(self.multipliers)):
            # Features basées sur l'historique récent
            recent_mults = self.multipliers[i-5:i]

            feature_vector = [
                np.mean(recent_mults),           # Moyenne récente
                np.std(recent_mults),            # Volatilité récente
                np.max(recent_mults),            # Maximum récent
                np.min(recent_mults),            # Minimum récent
                recent_mults[-1],                # Dernier multiplicateur
                np.mean(np.diff(recent_mults)),  # Tendance moyenne
                len([m for m in recent_mults if m <= self.CRASH_THRESHOLD]),  # Nombre de crashs récents
                i / len(self.multipliers)       # Position relative dans la session
            ]

            features.append(feature_vector)
            targets.append(self.multipliers[i])

        return np.array(features), np.array(targets)
    
    def build_pattern_database(self):
        """Construit une base de données des motifs récurrents"""
        if len(self.multipliers) < 50:
            return
            
        # Analyse des séquences de 2 à 5 tours
        for seq_len in range(2, 6):
            sequences = []
            for i in range(len(self.multipliers) - seq_len):
                seq = tuple(self.multipliers[i:i+seq_len])
                sequences.append(seq)
            
            # Comptage et stockage des motifs fréquents
            seq_counts = Counter(sequences)
            self.patterns_db[seq_len] = {
                'sequences': seq_counts,
                'most_common': seq_counts.most_common(5)
            }
    
    def analyze_history(self):
        """Analyse approfondie de l'historique"""
        if not self.multipliers:
            return "Aucune donnée disponible"
            
        # Statistiques de base améliorées
        stats = {
            'count': len(self.multipliers),
            'mean': np.mean(self.multipliers),
            'median': np.median(self.multipliers),
            'std_dev': np.std(self.multipliers),
            'min': min(self.multipliers),
            'max': max(self.multipliers),
            'q1': np.percentile(self.multipliers, 25),
            'q3': np.percentile(self.multipliers, 75)
        }
        
        # Analyse temporelle
        time_stats = ""
        if hasattr(self, 'df') and 'datetime' in self.df.columns:
            hourly = self.df.groupby(self.df['datetime'].dt.hour)['multiplier'].mean()
            time_stats = "\nMoyenne par heure:\n" + hourly.to_string()
        
        # Détection des séquences
        sequence_analysis = self.analyze_sequences()
        
        # Analyse des risques
        risk_analysis = self.risk_assessment()
        
        # Préparation du rapport
        report = f"""
=== ANALYSE AVANCÉE DE L'HISTORIQUE ===
Parties analysées: {stats['count']}
Statistiques des multiplicateurs:
- Moyenne: {stats['mean']:.2f}x
- Médiane: {stats['median']:.2f}x
- Écart-type: {stats['std_dev']:.2f}
- Minimum: {stats['min']:.2f}x
- Maximum: {stats['max']:.2f}x
- Quartiles: Q1={stats['q1']:.2f}x | Q3={stats['q3']:.2f}x

{time_stats}

=== ANALYSE DES SÉQUENCES ===
{sequence_analysis}

=== ÉVALUATION DES RISQUES ===
{risk_analysis}
"""
        return report
    
    def analyze_sequences(self):
        """Analyse approfondie des séquences"""
        if not self.patterns_db:
            return "Base de données de motifs non initialisée"
            
        report = ""
        for seq_len, data in self.patterns_db.items():
            report += f"\nSéquences de {seq_len} tours:\n"
            for seq, count in data['most_common']:
                report += f"- {seq}: {count} occurrences ({count/(len(self.multipliers)-seq_len)*100:.1f}%)\n"
        
        return report
    
    def risk_assessment(self):
        """Évalue les risques basés sur l'historique"""
        high_risk = len([m for m in self.multipliers if m >= self.HIGH_RISK_THRESHOLD])
        high_risk_pct = high_risk / len(self.multipliers) * 100
        
        # Détection des séries dangereuses
        crash_series = 0
        for i in range(len(self.multipliers) - 4):
            if all(m < 1.1 for m in self.multipliers[i:i+4]):
                crash_series += 1
        
        return f"""
- Parties à haut risque (>={self.HIGH_RISK_THRESHOLD}x): {high_risk} ({high_risk_pct:.1f}%)
- Séries dangereuses (4x <1.1x): {crash_series}
- Pire série basse: {self.worst_low_streak()}
- Pire série haute: {self.worst_high_streak()}
"""
    
    def worst_low_streak(self):
        """Trouve la plus longue série de multiplicateurs bas"""
        current = 0
        max_streak = 0
        for m in self.multipliers:
            if m < self.LOW_THRESHOLD:
                current += 1
                max_streak = max(max_streak, current)
            else:
                current = 0
        return max_streak
    
    def worst_high_streak(self):
        """Trouve la plus longue série de multiplicateurs élevés"""
        current = 0
        max_streak = 0
        for m in self.multipliers:
            if m >= self.MEDIUM_THRESHOLD:
                current += 1
                max_streak = max(max_streak, current)
            else:
                current = 0
        return max_streak
    
    def predict_next(self, method='advanced_hybrid'):
        """Prédiction améliorée avec analyse de crash et modèles multiples"""
        if len(self.multipliers) < 20:
            return "Pas assez de données pour une prédiction fiable"

        # Derniers multiplicateurs
        last_10 = self.multipliers[-10:]

        # Préparation des features pour la prédiction
        if len(self.multipliers) >= 5:
            current_features = self.prepare_current_features()
        else:
            return "Pas assez d'historique pour une prédiction avancée"

        predictions = {}
        crash_probability = 0

        # Prédictions avec différents modèles
        try:
            if 'polynomial' in self.models:
                predictions['polynomial'] = self.models['polynomial'].predict([current_features])[0]
            if 'random_forest' in self.models:
                predictions['random_forest'] = self.models['random_forest'].predict([current_features])[0]
            if 'neural_network' in self.models:
                predictions['neural_network'] = self.models['neural_network'].predict([current_features])[0]

            # Prédiction de crash
            if self.crash_predictor is not None:
                crash_probability = self.crash_predictor.predict_proba([current_features])[0][1]
        except Exception as e:
            print(f"Erreur dans les prédictions avancées: {e}")

        # Méthode traditionnelle comme fallback
        weights = np.arange(1, len(last_10)+1)
        wma = np.average(last_10, weights=weights)
        pattern_pred = self.pattern_based_prediction()

        # Combinaison intelligente des prédictions
        if method == 'advanced_hybrid' and predictions:
            # Pondération basée sur la performance historique
            model_weights = {'polynomial': 0.2, 'random_forest': 0.4, 'neural_network': 0.4}
            prediction = sum(predictions.get(model, wma) * weight
                           for model, weight in model_weights.items())
        elif method == 'crash_aware':
            # Prédiction ajustée selon le risque de crash
            base_pred = predictions.get('random_forest', wma)
            prediction = base_pred * (1 - crash_probability * 0.5)
        else:
            # Méthodes traditionnelles
            if method == 'wma':
                prediction = wma
            elif method == 'pattern':
                prediction = pattern_pred
            else:
                prediction = (wma * 0.6 + pattern_pred * 0.4)

        # Analyse de la tendance et du risque
        trend = self.analyze_trend()
        risk_level = self.calculate_risk_level(crash_probability)
        confidence = self.calculate_confidence(predictions, crash_probability)

        # Suggestion de stratégie améliorée
        suggestion = self.generate_advanced_suggestion(prediction, crash_probability, risk_level)

        # Enregistrement de la prédiction pour évaluation future
        self.prediction_history.append({
            'timestamp': datetime.now(),
            'prediction': prediction,
            'crash_probability': crash_probability,
            'actual_result': None  # Sera mis à jour lors de l'ajout du résultat réel
        })

        return {
            'prediction': prediction,
            'crash_probability': crash_probability,
            'trend': trend,
            'risk_level': risk_level,
            'confidence': confidence,
            'suggestion': suggestion,
            'models_predictions': predictions,
            'volatility_index': self.volatility_index,
            'trend_momentum': self.trend_momentum
        }

    def prepare_current_features(self):
        """Prépare les features pour la prédiction actuelle"""
        recent_mults = self.multipliers[-5:]

        return [
            np.mean(recent_mults),
            np.std(recent_mults),
            np.max(recent_mults),
            np.min(recent_mults),
            recent_mults[-1],
            np.mean(np.diff(recent_mults)),
            len([m for m in recent_mults if m <= self.CRASH_THRESHOLD]),
            1.0  # Position relative (dernière position)
        ]

    def analyze_trend(self):
        """Analyse la tendance actuelle"""
        if len(self.multipliers) < 5:
            return "indéterminée"

        recent_trend = np.diff(self.multipliers[-5:])
        avg_trend = np.mean(recent_trend)

        if avg_trend > 0.1:
            return "fortement haussière"
        elif avg_trend > 0:
            return "légèrement haussière"
        elif avg_trend > -0.1:
            return "stable"
        elif avg_trend > -0.2:
            return "légèrement baissière"
        else:
            return "fortement baissière"

    def calculate_risk_level(self, crash_probability):
        """Calcule le niveau de risque"""
        if crash_probability > 0.7:
            return "TRÈS ÉLEVÉ"
        elif crash_probability > 0.5:
            return "ÉLEVÉ"
        elif crash_probability > 0.3:
            return "MODÉRÉ"
        elif crash_probability > 0.1:
            return "FAIBLE"
        else:
            return "TRÈS FAIBLE"

    def calculate_confidence(self, predictions, crash_probability):
        """Calcule la confiance de la prédiction"""
        base_confidence = min(90, 40 + len(self.multipliers))

        # Réduction de confiance si forte probabilité de crash
        if crash_probability > 0.5:
            base_confidence *= (1 - crash_probability * 0.3)

        # Réduction si les modèles divergent beaucoup
        if predictions and len(predictions) > 1:
            pred_values = list(predictions.values())
            if np.std(pred_values) > np.mean(pred_values) * 0.2:
                base_confidence *= 0.8

        return max(20, min(95, base_confidence))
    
    def pattern_based_prediction(self):
        """Prédiction basée sur les motifs récurrents"""
        if not self.patterns_db:
            return np.mean(self.multipliers[-3:])
            
        # Recherche des derniers motifs
        last_seq = tuple(self.multipliers[-self.sequence_length:])
        
        # Recherche dans la base de motifs
        for seq_len in range(min(5, len(self.multipliers)), 1, -1):
            if seq_len in self.patterns_db:
                sequences = self.patterns_db[seq_len]['sequences']
                if last_seq in sequences:
                    # Trouver le prochain mouvement le plus probable
                    # (Implémentation simplifiée)
                    return np.mean(self.multipliers[-seq_len:]) * 0.95
                    
        return np.mean(self.multipliers[-3:])
    
    def generate_suggestion(self, predicted_mult):
        """Génère une suggestion de stratégie basée sur la prédiction (méthode legacy)"""
        if predicted_mult < 1.3:
            return "Éviter de miser ou miser très bas (risque élevé)"
        elif 1.3 <= predicted_mult < 1.8:
            return "Mise modérée avec retrait rapide (1.3x-1.5x)"
        elif 1.8 <= predicted_mult < 3.0:
            return "Bonne opportunité - mise normale avec retrait progressif"
        else:
            return "Potentiel élevé - considérer une mise agressive avec retrait partiel"

    def generate_advanced_suggestion(self, predicted_mult, crash_probability, risk_level):
        """Génère une suggestion de stratégie avancée"""
        suggestions = []

        # Analyse du risque de crash
        if crash_probability > 0.7:
            suggestions.append("🚨 ALERTE CRASH IMMINENT - Ne pas miser!")
            return " | ".join(suggestions)
        elif crash_probability > 0.5:
            suggestions.append("⚠️ Risque de crash élevé - Mise très prudente recommandée")
        elif crash_probability > 0.3:
            suggestions.append("⚡ Risque modéré - Surveiller attentivement")

        # Suggestion basée sur la prédiction
        if predicted_mult < 1.2:
            suggestions.append("💰 Mise: ÉVITER (prédiction très basse)")
        elif 1.2 <= predicted_mult < 1.5:
            suggestions.append("💰 Mise: FAIBLE - Retrait à 1.2x-1.3x")
        elif 1.5 <= predicted_mult < 2.0:
            suggestions.append("💰 Mise: MODÉRÉE - Retrait à 1.4x-1.6x")
        elif 2.0 <= predicted_mult < 3.5:
            suggestions.append("💰 Mise: NORMALE - Retrait progressif 1.8x-2.2x")
        elif 3.5 <= predicted_mult < 5.0:
            suggestions.append("💰 Mise: AGRESSIVE - Retrait partiel à 2.5x, reste à 4x")
        else:
            suggestions.append("💰 Mise: TRÈS AGRESSIVE - Retrait échelonné 3x/5x/8x")

        # Conseils additionnels basés sur l'analyse
        if self.volatility_index > 0.5:
            suggestions.append("📊 Volatilité élevée - Réduire les mises")

        if hasattr(self, 'crash_patterns') and self.crash_patterns:
            last_crash_distance = self.crash_patterns.get('last_crash_distance', 0)
            avg_interval = self.crash_patterns.get('avg_interval', 10)

            if last_crash_distance > avg_interval * 1.5:
                suggestions.append("⏰ Crash statistiquement probable bientôt")
            elif last_crash_distance < avg_interval * 0.5:
                suggestions.append("✅ Zone relativement sûre post-crash")

        return " | ".join(suggestions)
    
    def plot_advanced_analysis(self):
        """Visualisation avancée des données"""
        if not hasattr(self, 'df'):
            return
            
        plt.figure(figsize=(15, 10))
        
        # Graphique principal
        plt.subplot(2, 2, 1)
        plt.plot(self.df['multiplier'], label='Multiplicateurs')
        plt.axhline(y=np.mean(self.multipliers), color='r', linestyle='--', label='Moyenne')
        plt.yscale('log')
        plt.title('Historique des Multiplicateurs')
        plt.xlabel('Partie')
        plt.ylabel('Multiplicateur (log)')
        plt.legend()
        plt.grid(True)
        
        # Distribution
        plt.subplot(2, 2, 2)
        plt.hist(self.multipliers, bins=50, log=True)
        plt.title('Distribution des Multiplicateurs')
        plt.xlabel('Multiplicateur')
        plt.ylabel('Fréquence (log)')
        plt.grid(True)
        
        # Analyse par heure
        if 'datetime' in self.df.columns:
            plt.subplot(2, 2, 3)
            hourly = self.df.groupby(self.df['datetime'].dt.hour)['multiplier'].mean()
            hourly.plot(kind='bar')
            plt.title('Moyenne par Heure de la Journée')
            plt.xlabel('Heure')
            plt.ylabel('Multiplicateur Moyen')
            plt.grid(True)
        
        # Courbe de tendance
        plt.subplot(2, 2, 4)
        X = np.arange(len(self.multipliers))
        plt.scatter(X, self.multipliers, alpha=0.3, label='Données')
        if self.model:
            X_pred = np.linspace(0, len(self.multipliers)+10, 100)
            y_pred = self.model.predict(X_pred.reshape(-1, 1))
            plt.plot(X_pred, y_pred, 'r-', label='Tendance')
        plt.yscale('log')
        plt.title('Tendance des Multiplicateurs')
        plt.xlabel('Partie')
        plt.ylabel('Multiplicateur (log)')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.show()
    
    def add_game_result(self, multiplier, bet_amount=None):
        """Ajoute un résultat à la session en cours et met à jour les modèles"""
        game_data = {
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'multiplier': multiplier,
            'bet': bet_amount,
            'is_crash': multiplier <= self.CRASH_THRESHOLD
        }
        self.session_data.append(game_data)
        self.multipliers.append(multiplier)

        # Évaluation des prédictions précédentes
        self.evaluate_predictions(multiplier)

        # Mise à jour des métriques en temps réel
        self.update_real_time_metrics()

        # Ré-entraînement périodique des modèles
        if len(self.multipliers) % 15 == 0:
            self.train_prediction_models()
            self.build_pattern_database()
            self.analyze_crash_patterns()

        return game_data

    def evaluate_predictions(self, actual_multiplier):
        """Évalue la précision des prédictions précédentes"""
        if not self.prediction_history:
            return

        # Mise à jour de la dernière prédiction
        if self.prediction_history and self.prediction_history[-1]['actual_result'] is None:
            last_pred = self.prediction_history[-1]
            last_pred['actual_result'] = actual_multiplier

            # Calcul de l'erreur de prédiction
            pred_error = abs(last_pred['prediction'] - actual_multiplier)
            last_pred['error'] = pred_error

            # Évaluation de la prédiction de crash
            was_crash = actual_multiplier <= self.CRASH_THRESHOLD
            predicted_crash = last_pred['crash_probability'] > 0.5

            # Mise à jour des métriques de précision
            self.accuracy_metrics['total_predictions'] += 1
            if was_crash:
                self.accuracy_metrics['actual_crashes'] += 1
            if predicted_crash:
                self.accuracy_metrics['crash_predictions'] += 1
            if (predicted_crash and was_crash) or (not predicted_crash and not was_crash):
                self.accuracy_metrics['correct_predictions'] += 1

    def update_real_time_metrics(self):
        """Met à jour les métriques en temps réel"""
        if len(self.multipliers) >= 10:
            recent_mults = self.multipliers[-10:]
            self.volatility_index = np.std(recent_mults) / np.mean(recent_mults)

        if len(self.multipliers) >= 5:
            recent_trend = np.diff(self.multipliers[-5:])
            self.trend_momentum = np.mean(recent_trend)

        # Calcul du score de risque global
        crash_rate = len([m for m in self.multipliers[-20:] if m <= self.CRASH_THRESHOLD]) / min(20, len(self.multipliers))
        self.risk_score = (self.volatility_index * 0.4 + crash_rate * 0.6) * 100

    def get_accuracy_report(self):
        """Génère un rapport de précision des prédictions"""
        if self.accuracy_metrics['total_predictions'] == 0:
            return "Aucune prédiction évaluée pour le moment"

        overall_accuracy = (self.accuracy_metrics['correct_predictions'] /
                          self.accuracy_metrics['total_predictions'] * 100)

        crash_precision = 0
        crash_recall = 0

        if self.accuracy_metrics['crash_predictions'] > 0:
            # Calcul approximatif de la précision des prédictions de crash
            crash_precision = 50  # Placeholder - nécessiterait un tracking plus détaillé

        if self.accuracy_metrics['actual_crashes'] > 0:
            crash_recall = 50  # Placeholder - nécessiterait un tracking plus détaillé

        return f"""
=== RAPPORT DE PRÉCISION ===
Prédictions totales: {self.accuracy_metrics['total_predictions']}
Précision globale: {overall_accuracy:.1f}%
Crashs réels: {self.accuracy_metrics['actual_crashes']}
Crashs prédits: {self.accuracy_metrics['crash_predictions']}
Précision crash: {crash_precision:.1f}%
Rappel crash: {crash_recall:.1f}%

Score de risque actuel: {self.risk_score:.1f}/100
Indice de volatilité: {self.volatility_index:.3f}
Momentum de tendance: {self.trend_momentum:.3f}
"""
    
    def session_report(self):
        """Génère un rapport complet sur la session en cours"""
        if not self.session_data:
            return "Aucune donnée dans la session en cours"
            
        multipliers = [g['multiplier'] for g in self.session_data]
        bets = [g.get('bet', 0) for g in self.session_data if 'bet' in g]
        
        # Calcul des stats
        stats = {
            'games': len(self.session_data),
            'avg_multiplier': np.mean(multipliers),
            'min_multiplier': min(multipliers),
            'max_multiplier': max(multipliers),
            'win_rate': len([m for m in multipliers if m >= 1.5]) / len(multipliers) * 100,
            'high_risk': len([m for m in multipliers if m >= self.HIGH_RISK_THRESHOLD])
        }
        
        # Calcul des gains si des mises sont enregistrées
        profit_analysis = ""
        if bets:
            profits = []
            for bet, mult in zip(bets, multipliers[:len(bets)]):
                profits.append(bet * mult - bet)
            
            stats['total_profit'] = sum(profits)
            stats['avg_profit'] = np.mean(profits) if profits else 0
            stats['profit_margin'] = stats['total_profit'] / sum(bets) * 100 if sum(bets) > 0 else 0
            
            profit_analysis = f"""
=== ANALYSE DE PROFIT ===
- Profit total: {stats['total_profit']:,.2f} MGA
- Profit moyen par partie: {stats['avg_profit']:,.2f} MGA
- Marge de profit: {stats['profit_margin']:.2f}%
"""
        
        # Préparation du rapport
        report = f"""
=== RAPPORT DE SESSION ===
Parties jouées: {stats['games']}
Multiplicateur moyen: {stats['avg_multiplier']:.2f}x
Minimum: {stats['min_multiplier']:.2f}x | Maximum: {stats['max_multiplier']:.2f}x
Taux de réussite (>1.5x): {stats['win_rate']:.1f}%
Parties à haut risque: {stats['high_risk']}

{profit_analysis}

Derniers résultats:
"""
        # Ajout des 5 derniers résultats
        for game in self.session_data[-5:]:
            report += f"{game['timestamp']} - {game['multiplier']:.2f}x"
            if 'bet' in game:
                report += f" | Mise: {game['bet']:,} MGA"
            report += "\n"
        
        return report

class AviatorGUI:
    def __init__(self):
        self.predictor = AdvancedAviatorPredictor()
        self.root = tk.Tk()
        self.setup_gui()
        self.auto_predict_active = False
        self.load_initial_data()

    def setup_gui(self):
        """Configure l'interface graphique moderne"""
        self.root.title("🛩️ Aviator Predictor Pro - Analyse Avancée des Crashs")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1a1a1a')

        # Style moderne
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#1a1a1a', foreground='#00ff88')
        style.configure('Subtitle.TLabel', font=('Arial', 12, 'bold'), background='#1a1a1a', foreground='#ffffff')
        style.configure('Info.TLabel', font=('Arial', 10), background='#1a1a1a', foreground='#cccccc')

        # Frame principal
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Titre
        title_label = ttk.Label(main_frame, text="🛩️ AVIATOR PREDICTOR PRO", style='Title.TLabel')
        title_label.pack(pady=(0, 20))

        # Frame supérieur - Contrôles
        control_frame = ttk.LabelFrame(main_frame, text="Contrôles", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # Entrée de données
        input_frame = ttk.Frame(control_frame)
        input_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(input_frame, text="Multiplicateur:").pack(side=tk.LEFT)
        self.mult_entry = ttk.Entry(input_frame, width=10)
        self.mult_entry.pack(side=tk.LEFT, padx=(5, 10))

        ttk.Label(input_frame, text="Mise (MGA):").pack(side=tk.LEFT)
        self.bet_entry = ttk.Entry(input_frame, width=10)
        self.bet_entry.pack(side=tk.LEFT, padx=(5, 10))

        ttk.Button(input_frame, text="➕ Ajouter Résultat",
                  command=self.add_result).pack(side=tk.LEFT, padx=(10, 0))

        # Boutons de prédiction
        pred_frame = ttk.Frame(control_frame)
        pred_frame.pack(fill=tk.X)

        ttk.Button(pred_frame, text="🔮 Prédiction Avancée",
                  command=self.predict_advanced).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(pred_frame, text="⚠️ Analyse Crash",
                  command=self.analyze_crash_risk).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(pred_frame, text="📊 Rapport Précision",
                  command=self.show_accuracy_report).pack(side=tk.LEFT, padx=(0, 5))

        # Auto-prédiction
        self.auto_var = tk.BooleanVar()
        ttk.Checkbutton(pred_frame, text="Auto-Prédiction",
                       variable=self.auto_var,
                       command=self.toggle_auto_predict).pack(side=tk.RIGHT)

        # Frame central - Résultats
        result_frame = ttk.LabelFrame(main_frame, text="Résultats et Prédictions", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Zone de texte avec scrollbar
        self.result_text = scrolledtext.ScrolledText(result_frame,
                                                    height=20,
                                                    bg='#2a2a2a',
                                                    fg='#ffffff',
                                                    font=('Consolas', 10))
        self.result_text.pack(fill=tk.BOTH, expand=True)

        # Frame inférieur - Statistiques en temps réel
        stats_frame = ttk.LabelFrame(main_frame, text="Statistiques en Temps Réel", padding=10)
        stats_frame.pack(fill=tk.X)

        # Labels de statistiques
        stats_inner = ttk.Frame(stats_frame)
        stats_inner.pack(fill=tk.X)

        self.stats_labels = {}
        stats_info = [
            ("Parties", "parties_count"),
            ("Dernière Prédiction", "last_prediction"),
            ("Probabilité Crash", "crash_prob"),
            ("Risque", "risk_level"),
            ("Volatilité", "volatility"),
            ("Précision", "accuracy")
        ]

        for label, key in stats_info:
            frame = ttk.Frame(stats_inner)
            frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

            ttk.Label(frame, text=f"{label}:", style='Info.TLabel').pack()
            self.stats_labels[key] = ttk.Label(frame, text="--", style='Subtitle.TLabel')
            self.stats_labels[key].pack()

        # Mise à jour initiale
        self.update_stats()
        self.log_message("🚀 Aviator Predictor Pro initialisé!")

    def load_initial_data(self):
        """Charge les données initiales si disponibles"""
        try:
            with open("document_sans_titre.pdf", "r", encoding="utf-8") as f:
                text = f.read()
            self.predictor.load_data(text)
            self.log_message("✅ Données historiques chargées avec succès!")
            self.update_stats()
        except Exception as e:
            self.log_message(f"⚠️ Aucune donnée historique trouvée: {e}")
            self.log_message("Le système fonctionnera en mode apprentissage")

    def log_message(self, message):
        """Ajoute un message au log avec timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, formatted_message)
        self.result_text.see(tk.END)
        self.root.update_idletasks()

    def add_result(self):
        """Ajoute un résultat de partie"""
        try:
            mult_text = self.mult_entry.get().strip()
            bet_text = self.bet_entry.get().strip()

            if not mult_text:
                messagebox.showerror("Erreur", "Veuillez entrer un multiplicateur")
                return

            multiplier = float(mult_text)
            bet_amount = int(bet_text.replace(',', '')) if bet_text else None

            # Ajouter le résultat
            game = self.predictor.add_game_result(multiplier, bet_amount)

            # Log du résultat
            crash_indicator = "💥" if game['is_crash'] else "✅"
            bet_info = f" | Mise: {bet_amount:,} MGA" if bet_amount else ""
            self.log_message(f"{crash_indicator} Résultat: {multiplier:.2f}x{bet_info}")

            # Prédiction automatique si activée
            if self.auto_var.get():
                self.predict_advanced(auto=True)

            # Mise à jour des stats
            self.update_stats()

            # Nettoyer les champs
            self.mult_entry.delete(0, tk.END)
            self.bet_entry.delete(0, tk.END)

        except ValueError:
            messagebox.showerror("Erreur", "Veuillez entrer des valeurs numériques valides")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ajout: {e}")

    def predict_advanced(self, auto=False):
        """Effectue une prédiction avancée"""
        try:
            pred = self.predictor.predict_next('advanced_hybrid')

            if isinstance(pred, str):
                self.log_message(f"⚠️ {pred}")
                return

            # Formatage de la prédiction
            prefix = "🤖 [AUTO]" if auto else "🔮"
            self.log_message(f"\n{prefix} === PRÉDICTION AVANCÉE ===")
            self.log_message(f"📈 Multiplicateur estimé: {pred['prediction']:.2f}x")
            self.log_message(f"💥 Probabilité de crash: {pred['crash_probability']:.1%}")
            self.log_message(f"📊 Tendance: {pred['trend']}")
            self.log_message(f"⚠️ Niveau de risque: {pred['risk_level']}")
            self.log_message(f"🎯 Confiance: {pred['confidence']:.1f}%")
            self.log_message(f"💡 {pred['suggestion']}")

            if 'models_predictions' in pred and pred['models_predictions']:
                self.log_message("🧠 Prédictions des modèles:")
                for model, value in pred['models_predictions'].items():
                    self.log_message(f"   • {model}: {value:.2f}x")

            self.log_message("=" * 50)

        except Exception as e:
            self.log_message(f"❌ Erreur lors de la prédiction: {e}")

    def analyze_crash_risk(self):
        """Analyse spécifique du risque de crash"""
        try:
            if len(self.predictor.multipliers) < 5:
                self.log_message("⚠️ Pas assez de données pour l'analyse de crash")
                return

            self.log_message("\n💥 === ANALYSE DU RISQUE DE CRASH ===")

            # Analyse des patterns de crash
            if hasattr(self.predictor, 'crash_patterns') and self.predictor.crash_patterns:
                patterns = self.predictor.crash_patterns
                self.log_message(f"📊 Taux de crash: {patterns['crash_rate']:.1%}")
                self.log_message(f"⏱️ Intervalle moyen entre crashs: {patterns['avg_interval']:.1f} parties")
                self.log_message(f"🎯 Distance depuis dernier crash: {patterns['last_crash_distance']} parties")

                # Prédiction basée sur les intervalles
                if patterns['last_crash_distance'] > patterns['avg_interval'] * 1.2:
                    self.log_message("🚨 ATTENTION: Crash statistiquement probable!")
                elif patterns['last_crash_distance'] < patterns['avg_interval'] * 0.3:
                    self.log_message("✅ Zone relativement sûre (post-crash récent)")
                else:
                    self.log_message("⚖️ Risque normal selon les patterns historiques")

            # Analyse de volatilité
            self.log_message(f"📈 Indice de volatilité: {self.predictor.volatility_index:.3f}")
            self.log_message(f"🎢 Momentum de tendance: {self.predictor.trend_momentum:.3f}")
            self.log_message(f"⚠️ Score de risque global: {self.predictor.risk_score:.1f}/100")

            self.log_message("=" * 50)

        except Exception as e:
            self.log_message(f"❌ Erreur lors de l'analyse: {e}")

    def show_accuracy_report(self):
        """Affiche le rapport de précision"""
        try:
            report = self.predictor.get_accuracy_report()
            self.log_message(f"\n📊 {report}")
        except Exception as e:
            self.log_message(f"❌ Erreur lors du rapport: {e}")

    def toggle_auto_predict(self):
        """Active/désactive la prédiction automatique"""
        if self.auto_var.get():
            self.log_message("🤖 Auto-prédiction ACTIVÉE")
        else:
            self.log_message("🤖 Auto-prédiction DÉSACTIVÉE")

    def update_stats(self):
        """Met à jour les statistiques en temps réel"""
        try:
            # Nombre de parties
            self.stats_labels['parties_count'].config(text=str(len(self.predictor.multipliers)))

            # Dernière prédiction
            if self.predictor.prediction_history:
                last_pred = self.predictor.prediction_history[-1]['prediction']
                self.stats_labels['last_prediction'].config(text=f"{last_pred:.2f}x")
            else:
                self.stats_labels['last_prediction'].config(text="--")

            # Probabilité de crash (dernière)
            if self.predictor.prediction_history:
                crash_prob = self.predictor.prediction_history[-1]['crash_probability']
                self.stats_labels['crash_prob'].config(text=f"{crash_prob:.1%}")
            else:
                self.stats_labels['crash_prob'].config(text="--")

            # Niveau de risque
            if self.predictor.risk_score > 0:
                if self.predictor.risk_score > 70:
                    risk_text = "ÉLEVÉ"
                    color = "#ff4444"
                elif self.predictor.risk_score > 40:
                    risk_text = "MODÉRÉ"
                    color = "#ffaa44"
                else:
                    risk_text = "FAIBLE"
                    color = "#44ff44"
                self.stats_labels['risk_level'].config(text=risk_text, foreground=color)
            else:
                self.stats_labels['risk_level'].config(text="--")

            # Volatilité
            if self.predictor.volatility_index > 0:
                self.stats_labels['volatility'].config(text=f"{self.predictor.volatility_index:.3f}")
            else:
                self.stats_labels['volatility'].config(text="--")

            # Précision
            if self.predictor.accuracy_metrics['total_predictions'] > 0:
                accuracy = (self.predictor.accuracy_metrics['correct_predictions'] /
                          self.predictor.accuracy_metrics['total_predictions'] * 100)
                self.stats_labels['accuracy'].config(text=f"{accuracy:.1f}%")
            else:
                self.stats_labels['accuracy'].config(text="--")

        except Exception as e:
            print(f"Erreur mise à jour stats: {e}")

    def run(self):
        """Lance l'interface graphique"""
        self.root.mainloop()

# Interface Utilisateur Améliorée
def main():
    """Lance l'application avec interface graphique"""
    try:
        app = AviatorGUI()
        app.run()
    except Exception as e:
        print(f"Erreur lors du lancement de l'interface: {e}")
        print("Lancement en mode console...")
        console_main()

def console_main():
    """Interface console de fallback"""
    predictor = AdvancedAviatorPredictor()

    print("🛩️ Aviator Predictor Pro - Mode Console")
    try:
        with open("document_sans_titre.pdf", "r", encoding="utf-8") as f:
            text = f.read()
        predictor.load_data(text)
        print("✅ Données historiques chargées!")
    except Exception as e:
        print(f"⚠️ Pas de données historiques: {e}")

    while True:
        print("\n=== MENU PRINCIPAL ===")
        print("1. Ajouter un résultat")
        print("2. Prédiction avancée")
        print("3. Analyse de crash")
        print("4. Rapport de précision")
        print("5. Analyse historique")
        print("6. Quitter")

        choice = input("Votre choix: ")

        if choice == "1":
            try:
                mult = float(input("Multiplicateur: "))
                bet = input("Mise (facultatif): ")
                bet_amount = int(bet.replace(',', '')) if bet else None

                game = predictor.add_game_result(mult, bet_amount)
                print(f"✅ Résultat ajouté: {game['multiplier']:.2f}x")

            except ValueError:
                print("❌ Valeurs invalides")

        elif choice == "2":
            pred = predictor.predict_next('advanced_hybrid')
            if isinstance(pred, dict):
                print(f"\n🔮 Prédiction: {pred['prediction']:.2f}x")
                print(f"💥 Crash: {pred['crash_probability']:.1%}")
                print(f"💡 {pred['suggestion']}")
            else:
                print(pred)

        elif choice == "3":
            # Analyse de crash pour console
            if len(predictor.multipliers) < 5:
                print("⚠️ Pas assez de données pour l'analyse")
            else:
                print("\n💥 === ANALYSE DU RISQUE DE CRASH ===")
                if hasattr(predictor, 'crash_patterns') and predictor.crash_patterns:
                    patterns = predictor.crash_patterns
                    print(f"Taux de crash: {patterns['crash_rate']:.1%}")
                    print(f"Intervalle moyen: {patterns['avg_interval']:.1f} parties")
                    print(f"Distance dernier crash: {patterns['last_crash_distance']}")
                print(f"Volatilité: {predictor.volatility_index:.3f}")
                print(f"Score de risque: {predictor.risk_score:.1f}/100")

        elif choice == "4":
            print(predictor.get_accuracy_report())

        elif choice == "5":
            print(predictor.analyze_history())

        elif choice == "6":
            break

if __name__ == "__main__":
    main()