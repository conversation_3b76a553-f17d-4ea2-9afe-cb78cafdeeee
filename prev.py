import re
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter, deque
from datetime import datetime
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import make_pipeline
import warnings
warnings.filterwarnings('ignore')

class AdvancedAviatorPredictor:
    def __init__(self, window_size=10, sequence_length=3):
        self.multipliers = []
        self.timestamps = []
        self.bets = []
        self.window_size = window_size
        self.sequence_length = sequence_length
        self.session_data = []
        self.model = None
        self.patterns_db = {}
        
        # Configuration des seuils
        self.LOW_THRESHOLD = 1.5
        self.MEDIUM_THRESHOLD = 5.0
        self.HIGH_RISK_THRESHOLD = 10.0
        
    def load_data(self, text):
        """Charge les données à partir du texte brut avec une extraction améliorée"""
        # Extraction des multiplicateurs avec timestamp
        pattern = r"(\d{2}:\d{2}).*?(\d+\.\d+)x"
        matches = re.findall(pattern, text)
        
        for time, mult in matches:
            self.timestamps.append(time)
            self.multipliers.append(float(mult))
        
        # Extraction des mises avec une regex plus robuste
        bet_pattern = r"Miser Auto[^\d]*(\d{1,3}(?:,\d{3})*)"
        self.bets = [int(b.replace(',', '')) for b in re.findall(bet_pattern, text)]
        
        # Construction du DataFrame pour analyse
        self.df = pd.DataFrame({
            'timestamp': self.timestamps,
            'multiplier': self.multipliers
        })
        
        # Conversion des timestamps en datetime
        try:
            self.df['datetime'] = pd.to_datetime(self.df['timestamp'], format='%H:%M')
        except:
            self.df['datetime'] = pd.to_datetime(self.df['timestamp'], errors='coerce')
        
        print(f"Données chargées: {len(self.multipliers)} parties avec {len(self.bets)} mises enregistrées")
        
        # Entraînement initial du modèle
        self.train_prediction_model()
        
        # Construction de la base de données de motifs
        self.build_pattern_database()
    
    def train_prediction_model(self):
        """Entraîne un modèle de prédiction polynomial"""
        if len(self.multipliers) < 20:
            print("Attention: Peu de données disponibles pour un modèle fiable")
            return
            
        X = np.arange(len(self.multipliers)).reshape(-1, 1)
        y = np.array(self.multipliers)
        
        # Modèle de régression polynomiale degré 2
        self.model = make_pipeline(
            PolynomialFeatures(degree=2),
            LinearRegression()
        )
        self.model.fit(X, y)
    
    def build_pattern_database(self):
        """Construit une base de données des motifs récurrents"""
        if len(self.multipliers) < 50:
            return
            
        # Analyse des séquences de 2 à 5 tours
        for seq_len in range(2, 6):
            sequences = []
            for i in range(len(self.multipliers) - seq_len):
                seq = tuple(self.multipliers[i:i+seq_len])
                sequences.append(seq)
            
            # Comptage et stockage des motifs fréquents
            seq_counts = Counter(sequences)
            self.patterns_db[seq_len] = {
                'sequences': seq_counts,
                'most_common': seq_counts.most_common(5)
            }
    
    def analyze_history(self):
        """Analyse approfondie de l'historique"""
        if not self.multipliers:
            return "Aucune donnée disponible"
            
        # Statistiques de base améliorées
        stats = {
            'count': len(self.multipliers),
            'mean': np.mean(self.multipliers),
            'median': np.median(self.multipliers),
            'std_dev': np.std(self.multipliers),
            'min': min(self.multipliers),
            'max': max(self.multipliers),
            'q1': np.percentile(self.multipliers, 25),
            'q3': np.percentile(self.multipliers, 75)
        }
        
        # Analyse temporelle
        time_stats = ""
        if hasattr(self, 'df') and 'datetime' in self.df.columns:
            hourly = self.df.groupby(self.df['datetime'].dt.hour)['multiplier'].mean()
            time_stats = "\nMoyenne par heure:\n" + hourly.to_string()
        
        # Détection des séquences
        sequence_analysis = self.analyze_sequences()
        
        # Analyse des risques
        risk_analysis = self.risk_assessment()
        
        # Préparation du rapport
        report = f"""
=== ANALYSE AVANCÉE DE L'HISTORIQUE ===
Parties analysées: {stats['count']}
Statistiques des multiplicateurs:
- Moyenne: {stats['mean']:.2f}x
- Médiane: {stats['median']:.2f}x
- Écart-type: {stats['std_dev']:.2f}
- Minimum: {stats['min']:.2f}x
- Maximum: {stats['max']:.2f}x
- Quartiles: Q1={stats['q1']:.2f}x | Q3={stats['q3']:.2f}x

{time_stats}

=== ANALYSE DES SÉQUENCES ===
{sequence_analysis}

=== ÉVALUATION DES RISQUES ===
{risk_analysis}
"""
        return report
    
    def analyze_sequences(self):
        """Analyse approfondie des séquences"""
        if not self.patterns_db:
            return "Base de données de motifs non initialisée"
            
        report = ""
        for seq_len, data in self.patterns_db.items():
            report += f"\nSéquences de {seq_len} tours:\n"
            for seq, count in data['most_common']:
                report += f"- {seq}: {count} occurrences ({count/(len(self.multipliers)-seq_len)*100:.1f}%)\n"
        
        return report
    
    def risk_assessment(self):
        """Évalue les risques basés sur l'historique"""
        high_risk = len([m for m in self.multipliers if m >= self.HIGH_RISK_THRESHOLD])
        high_risk_pct = high_risk / len(self.multipliers) * 100
        
        # Détection des séries dangereuses
        crash_series = 0
        for i in range(len(self.multipliers) - 4):
            if all(m < 1.1 for m in self.multipliers[i:i+4]):
                crash_series += 1
        
        return f"""
- Parties à haut risque (>={self.HIGH_RISK_THRESHOLD}x): {high_risk} ({high_risk_pct:.1f}%)
- Séries dangereuses (4x <1.1x): {crash_series}
- Pire série basse: {self.worst_low_streak()}
- Pire série haute: {self.worst_high_streak()}
"""
    
    def worst_low_streak(self):
        """Trouve la plus longue série de multiplicateurs bas"""
        current = 0
        max_streak = 0
        for m in self.multipliers:
            if m < self.LOW_THRESHOLD:
                current += 1
                max_streak = max(max_streak, current)
            else:
                current = 0
        return max_streak
    
    def worst_high_streak(self):
        """Trouve la plus longue série de multiplicateurs élevés"""
        current = 0
        max_streak = 0
        for m in self.multipliers:
            if m >= self.MEDIUM_THRESHOLD:
                current += 1
                max_streak = max(max_streak, current)
            else:
                current = 0
        return max_streak
    
    def predict_next(self, method='hybrid'):
        """Prédiction améliorée avec plusieurs méthodes"""
        if len(self.multipliers) < 20:
            return "Pas assez de données pour une prédiction fiable"
            
        # Derniers multiplicateurs
        last_5 = self.multipliers[-5:]
        last_10 = self.multipliers[-10:]
        
        # Méthode 1: Moyenne mobile pondérée
        weights = np.arange(1, len(last_10)+1)
        wma = np.average(last_10, weights=weights)
        
        # Méthode 2: Modèle polynomial
        poly_pred = self.model.predict([[len(self.multipliers)]])[0]
        
        # Méthode 3: Analyse des motifs
        pattern_pred = self.pattern_based_prediction()
        
        # Combinaison des méthodes
        if method == 'hybrid':
            prediction = (wma * 0.4 + poly_pred * 0.4 + pattern_pred * 0.2)
        elif method == 'poly':
            prediction = poly_pred
        elif method == 'wma':
            prediction = wma
        else:
            prediction = pattern_pred
        
        # Analyse de la tendance
        trend = "haussière" if prediction > np.mean(last_10) else "baissière"
        
        # Calcul de la confiance
        confidence = min(90, 30 + len(self.multipliers)/2)
        
        # Suggestion de stratégie
        suggestion = self.generate_suggestion(prediction)
        
        return {
            'prediction': prediction,
            'trend': trend,
            'confidence': confidence,
            'suggestion': suggestion,
            'methods': {
                'weighted_moving_avg': wma,
                'poly_model': poly_pred,
                'pattern_based': pattern_pred
            }
        }
    
    def pattern_based_prediction(self):
        """Prédiction basée sur les motifs récurrents"""
        if not self.patterns_db:
            return np.mean(self.multipliers[-3:])
            
        # Recherche des derniers motifs
        last_seq = tuple(self.multipliers[-self.sequence_length:])
        
        # Recherche dans la base de motifs
        for seq_len in range(min(5, len(self.multipliers)), 1, -1):
            if seq_len in self.patterns_db:
                sequences = self.patterns_db[seq_len]['sequences']
                if last_seq in sequences:
                    # Trouver le prochain mouvement le plus probable
                    # (Implémentation simplifiée)
                    return np.mean(self.multipliers[-seq_len:]) * 0.95
                    
        return np.mean(self.multipliers[-3:])
    
    def generate_suggestion(self, predicted_mult):
        """Génère une suggestion de stratégie basée sur la prédiction"""
        if predicted_mult < 1.3:
            return "Éviter de miser ou miser très bas (risque élevé)"
        elif 1.3 <= predicted_mult < 1.8:
            return "Mise modérée avec retrait rapide (1.3x-1.5x)"
        elif 1.8 <= predicted_mult < 3.0:
            return "Bonne opportunité - mise normale avec retrait progressif"
        else:
            return "Potentiel élevé - considérer une mise agressive avec retrait partiel"
    
    def plot_advanced_analysis(self):
        """Visualisation avancée des données"""
        if not hasattr(self, 'df'):
            return
            
        plt.figure(figsize=(15, 10))
        
        # Graphique principal
        plt.subplot(2, 2, 1)
        plt.plot(self.df['multiplier'], label='Multiplicateurs')
        plt.axhline(y=np.mean(self.multipliers), color='r', linestyle='--', label='Moyenne')
        plt.yscale('log')
        plt.title('Historique des Multiplicateurs')
        plt.xlabel('Partie')
        plt.ylabel('Multiplicateur (log)')
        plt.legend()
        plt.grid(True)
        
        # Distribution
        plt.subplot(2, 2, 2)
        plt.hist(self.multipliers, bins=50, log=True)
        plt.title('Distribution des Multiplicateurs')
        plt.xlabel('Multiplicateur')
        plt.ylabel('Fréquence (log)')
        plt.grid(True)
        
        # Analyse par heure
        if 'datetime' in self.df.columns:
            plt.subplot(2, 2, 3)
            hourly = self.df.groupby(self.df['datetime'].dt.hour)['multiplier'].mean()
            hourly.plot(kind='bar')
            plt.title('Moyenne par Heure de la Journée')
            plt.xlabel('Heure')
            plt.ylabel('Multiplicateur Moyen')
            plt.grid(True)
        
        # Courbe de tendance
        plt.subplot(2, 2, 4)
        X = np.arange(len(self.multipliers))
        plt.scatter(X, self.multipliers, alpha=0.3, label='Données')
        if self.model:
            X_pred = np.linspace(0, len(self.multipliers)+10, 100)
            y_pred = self.model.predict(X_pred.reshape(-1, 1))
            plt.plot(X_pred, y_pred, 'r-', label='Tendance')
        plt.yscale('log')
        plt.title('Tendance des Multiplicateurs')
        plt.xlabel('Partie')
        plt.ylabel('Multiplicateur (log)')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.show()
    
    def add_game_result(self, multiplier, bet_amount=None):
        """Ajoute un résultat à la session en cours et met à jour le modèle"""
        game_data = {
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'multiplier': multiplier,
            'bet': bet_amount
        }
        self.session_data.append(game_data)
        self.multipliers.append(multiplier)
        
        # Mise à jour du modèle
        if len(self.multipliers) % 10 == 0:  # Ré-entraînement périodique
            self.train_prediction_model()
            self.build_pattern_database()
        
        return game_data
    
    def session_report(self):
        """Génère un rapport complet sur la session en cours"""
        if not self.session_data:
            return "Aucune donnée dans la session en cours"
            
        multipliers = [g['multiplier'] for g in self.session_data]
        bets = [g.get('bet', 0) for g in self.session_data if 'bet' in g]
        
        # Calcul des stats
        stats = {
            'games': len(self.session_data),
            'avg_multiplier': np.mean(multipliers),
            'min_multiplier': min(multipliers),
            'max_multiplier': max(multipliers),
            'win_rate': len([m for m in multipliers if m >= 1.5]) / len(multipliers) * 100,
            'high_risk': len([m for m in multipliers if m >= self.HIGH_RISK_THRESHOLD])
        }
        
        # Calcul des gains si des mises sont enregistrées
        profit_analysis = ""
        if bets:
            profits = []
            for i, (bet, mult) in enumerate(zip(bets, multipliers[:len(bets)])):
                profits.append(bet * mult - bet)
            
            stats['total_profit'] = sum(profits)
            stats['avg_profit'] = np.mean(profits) if profits else 0
            stats['profit_margin'] = stats['total_profit'] / sum(bets) * 100 if sum(bets) > 0 else 0
            
            profit_analysis = f"""
=== ANALYSE DE PROFIT ===
- Profit total: {stats['total_profit']:,.2f} MGA
- Profit moyen par partie: {stats['avg_profit']:,.2f} MGA
- Marge de profit: {stats['profit_margin']:.2f}%
"""
        
        # Préparation du rapport
        report = f"""
=== RAPPORT DE SESSION ===
Parties jouées: {stats['games']}
Multiplicateur moyen: {stats['avg_multiplier']:.2f}x
Minimum: {stats['min_multiplier']:.2f}x | Maximum: {stats['max_multiplier']:.2f}x
Taux de réussite (>1.5x): {stats['win_rate']:.1f}%
Parties à haut risque: {stats['high_risk']}

{profit_analysis}

Derniers résultats:
"""
        # Ajout des 5 derniers résultats
        for game in self.session_data[-5:]:
            report += f"{game['timestamp']} - {game['multiplier']:.2f}x"
            if 'bet' in game:
                report += f" | Mise: {game['bet']:,} MGA"
            report += "\n"
        
        return report

# Interface Utilisateur Améliorée
def main():
    predictor = AdvancedAviatorPredictor()
    
    # Chargement des données initiales
    print("Initialisation du système de prédiction Aviator...")
    try:
        with open("document_sans_titre.pdf", "r", encoding="utf-8") as f:
            text = f.read()
        predictor.load_data(text)
        print("Données historiques chargées avec succès!")
    except Exception as e:
        print(f"Erreur lors du chargement des données: {e}")
        print("Le système fonctionnera sans historique initial")
    
    while True:
        print("\n=== MENU PRINCIPAL ===")
        print("1. Ajouter un résultat de partie")
        print("2. Obtenir une prédiction")
        print("3. Voir l'analyse historique")
        print("4. Voir le rapport de session")
        print("5. Visualisations avancées")
        print("6. Quitter")
        
        choice = input("Votre choix: ")
        
        if choice == "1":
            try:
                mult = float(input("Multiplicateur obtenu (ex: 2.15): "))
                bet = input("Montant misé (MGA, facultatif): ")
                bet_amount = int(bet.replace(',', '')) if bet else None
                
                game = predictor.add_game_result(mult, bet_amount)
                print(f"\nRésultat enregistré: {game['timestamp']} - {game['multiplier']:.2f}x")
                
                # Afficher une prédiction après l'ajout
                pred = predictor.predict_next()
                if isinstance(pred, dict):
                    print(f"\nProchaine prédiction: {pred['prediction']:.2f}x ({pred['trend']})")
                    print(f"Suggestion: {pred['suggestion']}")
            except ValueError:
                print("Erreur: Veuillez entrer des valeurs numériques valides")
        
        elif choice == "2":
            method = input("Méthode de prédiction (hybrid/poly/wma/pattern): ") or 'hybrid'
            pred = predictor.predict_next(method)
            
            if isinstance(pred, str):
                print(pred)
            else:
                print("\n=== PRÉDICTION ===")
                print(f"Multiplicateur estimé: {pred['prediction']:.2f}x")
                print(f"Tendance: {pred['trend']}")
                print(f"Confiance: {pred['confidence']:.1f}%")
                print(f"\nSuggestion: {pred['suggestion']}")
                
                print("\nDétails des méthodes:")
                print(f"- Moyenne mobile pondérée: {pred['methods']['weighted_moving_avg']:.2f}x")
                print(f"- Modèle polynomial: {pred['methods']['poly_model']:.2f}x")
                print(f"- Basé sur motifs: {pred['methods']['pattern_based']:.2f}x")
        
        elif choice == "3":
            print(predictor.analyze_history())
        
        elif choice == "4":
            print(predictor.session_report())
        
        elif choice == "5":
            predictor.plot_advanced_analysis()
        
        elif choice == "6":
            print("Fermeture du système...")
            break
        
        else:
            print("Choix invalide, veuillez réessayer")

if __name__ == "__main__":
    main()