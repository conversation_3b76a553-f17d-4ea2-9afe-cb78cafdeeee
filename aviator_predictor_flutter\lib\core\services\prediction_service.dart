import 'dart:math';
import '../../data/models/game_result.dart';
import '../../data/models/prediction.dart';

class PredictionService {
  static const double crashThreshold = 1.1;
  static const int minDataPoints = 20;
  static const int windowSize = 15;
  
  // Advanced prediction using multiple algorithms
  static Prediction predictNext(List<GameResult> history, {String method = 'hybrid'}) {
    if (history.length < minDataPoints) {
      return _createBasicPrediction("Pas assez de données pour une prédiction fiable");
    }
    
    final multipliers = history.map((r) => r.multiplier).toList();
    final recentMultipliers = multipliers.take(windowSize).toList();
    
    // Calculate various metrics
    final volatilityIndex = _calculateVolatility(recentMultipliers);
    final trendMomentum = _calculateTrendMomentum(recentMultipliers);
    final crashProbability = _calculateCrashProbability(history);
    
    // Different prediction models
    final modelPredictions = <String, double>{};
    
    // 1. Weighted Moving Average
    modelPredictions['wma'] = _weightedMovingAverage(recentMultipliers);
    
    // 2. Exponential Smoothing
    modelPredictions['exponential'] = _exponentialSmoothing(recentMultipliers);
    
    // 3. Pattern-based prediction
    modelPredictions['pattern'] = _patternBasedPrediction(multipliers);
    
    // 4. Regression-based prediction
    modelPredictions['regression'] = _regressionPrediction(recentMultipliers);
    
    // 5. Neural network simulation
    modelPredictions['neural'] = _neuralNetworkSimulation(recentMultipliers, volatilityIndex);
    
    // Combine predictions based on method
    double finalPrediction;
    switch (method) {
      case 'conservative':
        finalPrediction = _conservativePrediction(modelPredictions, crashProbability);
        break;
      case 'aggressive':
        finalPrediction = _aggressivePrediction(modelPredictions, crashProbability);
        break;
      case 'adaptive':
        finalPrediction = _adaptivePrediction(modelPredictions, volatilityIndex, trendMomentum);
        break;
      default:
        finalPrediction = _hybridPrediction(modelPredictions, crashProbability, volatilityIndex);
    }
    
    // Adjust prediction based on crash probability
    if (crashProbability > 0.7) {
      finalPrediction = min(finalPrediction, 1.3);
    }
    
    final riskLevel = _calculateRiskLevel(crashProbability, volatilityIndex);
    final confidence = _calculateConfidence(modelPredictions, crashProbability, history.length);
    final suggestion = _generateAdvancedSuggestion(finalPrediction, crashProbability, riskLevel, volatilityIndex);
    
    return Prediction.create(
      predictedMultiplier: finalPrediction,
      crashProbability: crashProbability,
      riskLevel: riskLevel,
      confidence: confidence,
      suggestion: suggestion,
      method: method,
      modelPredictions: modelPredictions,
      volatilityIndex: volatilityIndex,
      trendMomentum: trendMomentum,
    );
  }
  
  static double _calculateVolatility(List<double> multipliers) {
    if (multipliers.length < 2) return 0.0;
    
    final mean = multipliers.reduce((a, b) => a + b) / multipliers.length;
    final variance = multipliers.map((x) => pow(x - mean, 2)).reduce((a, b) => a + b) / multipliers.length;
    return sqrt(variance) / mean;
  }
  
  static double _calculateTrendMomentum(List<double> multipliers) {
    if (multipliers.length < 2) return 0.0;
    
    double momentum = 0.0;
    for (int i = 1; i < multipliers.length; i++) {
      momentum += multipliers[i] - multipliers[i - 1];
    }
    return momentum / (multipliers.length - 1);
  }
  
  static double _calculateCrashProbability(List<GameResult> history) {
    if (history.length < 10) return 0.5;
    
    final recent = history.take(20).toList();
    final crashCount = recent.where((r) => r.isCrash).length;
    final baseProbability = crashCount / recent.length;
    
    // Analyze crash patterns
    final crashIntervals = _getCrashIntervals(history);
    final avgInterval = crashIntervals.isNotEmpty 
        ? crashIntervals.reduce((a, b) => a + b) / crashIntervals.length 
        : 10.0;
    
    final lastCrashDistance = _getLastCrashDistance(history);
    
    // Adjust probability based on distance from last crash
    double adjustedProbability = baseProbability;
    if (lastCrashDistance > avgInterval * 1.5) {
      adjustedProbability = min(0.9, baseProbability * 1.5);
    } else if (lastCrashDistance < avgInterval * 0.3) {
      adjustedProbability = max(0.1, baseProbability * 0.5);
    }
    
    return adjustedProbability.clamp(0.0, 1.0);
  }
  
  static List<int> _getCrashIntervals(List<GameResult> history) {
    final crashIndices = <int>[];
    for (int i = 0; i < history.length; i++) {
      if (history[i].isCrash) crashIndices.add(i);
    }
    
    final intervals = <int>[];
    for (int i = 1; i < crashIndices.length; i++) {
      intervals.add(crashIndices[i] - crashIndices[i - 1]);
    }
    
    return intervals;
  }
  
  static int _getLastCrashDistance(List<GameResult> history) {
    for (int i = 0; i < history.length; i++) {
      if (history[i].isCrash) return i;
    }
    return history.length;
  }
  
  static double _weightedMovingAverage(List<double> multipliers) {
    if (multipliers.isEmpty) return 1.5;
    
    double weightedSum = 0.0;
    double weightSum = 0.0;
    
    for (int i = 0; i < multipliers.length; i++) {
      final weight = i + 1;
      weightedSum += multipliers[i] * weight;
      weightSum += weight;
    }
    
    return weightedSum / weightSum;
  }
  
  static double _exponentialSmoothing(List<double> multipliers, {double alpha = 0.3}) {
    if (multipliers.isEmpty) return 1.5;
    if (multipliers.length == 1) return multipliers.first;
    
    double smoothed = multipliers.first;
    for (int i = 1; i < multipliers.length; i++) {
      smoothed = alpha * multipliers[i] + (1 - alpha) * smoothed;
    }
    
    return smoothed;
  }
  
  static double _patternBasedPrediction(List<double> multipliers) {
    if (multipliers.length < 6) return _weightedMovingAverage(multipliers);
    
    // Look for repeating patterns
    final patternLength = 3;
    final recentPattern = multipliers.take(patternLength).toList();
    
    // Find similar patterns in history
    final similarPatterns = <List<double>>[];
    for (int i = patternLength; i < multipliers.length - patternLength; i++) {
      final pattern = multipliers.skip(i).take(patternLength).toList();
      if (_patternsAreSimilar(recentPattern, pattern)) {
        if (i + patternLength < multipliers.length) {
          similarPatterns.add(multipliers.skip(i + patternLength).take(patternLength).toList());
        }
      }
    }
    
    if (similarPatterns.isNotEmpty) {
      final avgNext = similarPatterns
          .map((p) => p.first)
          .reduce((a, b) => a + b) / similarPatterns.length;
      return avgNext;
    }
    
    return _weightedMovingAverage(multipliers);
  }
  
  static bool _patternsAreSimilar(List<double> pattern1, List<double> pattern2, {double tolerance = 0.3}) {
    if (pattern1.length != pattern2.length) return false;
    
    for (int i = 0; i < pattern1.length; i++) {
      final diff = (pattern1[i] - pattern2[i]).abs();
      final avgValue = (pattern1[i] + pattern2[i]) / 2;
      if (diff > avgValue * tolerance) return false;
    }
    
    return true;
  }
  
  static double _regressionPrediction(List<double> multipliers) {
    if (multipliers.length < 3) return _weightedMovingAverage(multipliers);
    
    // Simple linear regression
    final n = multipliers.length;
    double sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;
    
    for (int i = 0; i < n; i++) {
      sumX += i;
      sumY += multipliers[i];
      sumXY += i * multipliers[i];
      sumX2 += i * i;
    }
    
    final slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    final intercept = (sumY - slope * sumX) / n;
    
    return slope * n + intercept;
  }
  
  static double _neuralNetworkSimulation(List<double> multipliers, double volatility) {
    if (multipliers.length < 3) return _weightedMovingAverage(multipliers);
    
    // Simplified neural network simulation
    final inputs = multipliers.take(3).toList();
    final weights = [0.4, 0.3, 0.3];
    
    double output = 0.0;
    for (int i = 0; i < inputs.length; i++) {
      output += inputs[i] * weights[i];
    }
    
    // Apply activation function (sigmoid-like)
    output = 1 / (1 + exp(-output + 2));
    output = output * 10; // Scale to reasonable multiplier range
    
    // Adjust for volatility
    if (volatility > 0.5) {
      output *= (1 - volatility * 0.2);
    }
    
    return output.clamp(1.0, 50.0);
  }
  
  static double _hybridPrediction(Map<String, double> models, double crashProb, double volatility) {
    final weights = <String, double>{
      'wma': 0.2,
      'exponential': 0.2,
      'pattern': 0.2,
      'regression': 0.2,
      'neural': 0.2,
    };
    
    // Adjust weights based on volatility
    if (volatility > 0.5) {
      weights['wma'] = 0.3;
      weights['exponential'] = 0.3;
      weights['neural'] = 0.1;
    }
    
    double prediction = 0.0;
    double totalWeight = 0.0;
    
    models.forEach((model, value) {
      final weight = weights[model] ?? 0.0;
      prediction += value * weight;
      totalWeight += weight;
    });
    
    return totalWeight > 0 ? prediction / totalWeight : 1.5;
  }
  
  static double _conservativePrediction(Map<String, double> models, double crashProb) {
    final values = models.values.toList()..sort();
    return values[values.length ~/ 3]; // Lower third
  }
  
  static double _aggressivePrediction(Map<String, double> models, double crashProb) {
    final values = models.values.toList()..sort();
    return values[(values.length * 2) ~/ 3]; // Upper third
  }
  
  static double _adaptivePrediction(Map<String, double> models, double volatility, double momentum) {
    if (volatility > 0.5) {
      return _conservativePrediction(models, 0.0);
    } else if (momentum > 0.1) {
      return _aggressivePrediction(models, 0.0);
    } else {
      return _hybridPrediction(models, 0.0, volatility);
    }
  }
  
  static String _calculateRiskLevel(double crashProb, double volatility) {
    final riskScore = crashProb * 0.7 + volatility * 0.3;
    
    if (riskScore > 0.8) return 'TRÈS ÉLEVÉ';
    if (riskScore > 0.6) return 'ÉLEVÉ';
    if (riskScore > 0.4) return 'MODÉRÉ';
    if (riskScore > 0.2) return 'FAIBLE';
    return 'TRÈS FAIBLE';
  }
  
  static double _calculateConfidence(Map<String, double> models, double crashProb, int dataPoints) {
    // Base confidence on data amount
    double confidence = min(95.0, 30.0 + dataPoints * 0.5);
    
    // Reduce confidence for high crash probability
    if (crashProb > 0.7) {
      confidence *= 0.7;
    }
    
    // Reduce confidence if models disagree significantly
    final values = models.values.toList();
    if (values.isNotEmpty) {
      final mean = values.reduce((a, b) => a + b) / values.length;
      final variance = values.map((v) => pow(v - mean, 2)).reduce((a, b) => a + b) / values.length;
      final stdDev = sqrt(variance);
      
      if (stdDev > mean * 0.3) {
        confidence *= 0.8;
      }
    }
    
    return confidence.clamp(20.0, 95.0);
  }
  
  static String _generateAdvancedSuggestion(double prediction, double crashProb, String riskLevel, double volatility) {
    final suggestions = <String>[];
    
    // Crash warning
    if (crashProb > 0.8) {
      suggestions.add('🚨 DANGER EXTRÊME - Éviter absolument');
    } else if (crashProb > 0.6) {
      suggestions.add('⚠️ RISQUE TRÈS ÉLEVÉ - Mise minimale uniquement');
    } else if (crashProb > 0.4) {
      suggestions.add('⚡ Risque modéré - Prudence recommandée');
    }
    
    // Betting strategy
    if (prediction < 1.2) {
      suggestions.add('💰 ÉVITER - Prédiction très basse');
    } else if (prediction < 1.5) {
      suggestions.add('💰 Mise FAIBLE - Sortie rapide à 1.2x');
    } else if (prediction < 2.0) {
      suggestions.add('💰 Mise MODÉRÉE - Sortie à 1.5x-1.7x');
    } else if (prediction < 3.5) {
      suggestions.add('💰 Mise NORMALE - Sortie progressive');
    } else {
      suggestions.add('💰 Mise AGRESSIVE - Sortie échelonnée');
    }
    
    // Volatility warning
    if (volatility > 0.6) {
      suggestions.add('📊 Volatilité extrême - Réduire les mises');
    } else if (volatility > 0.4) {
      suggestions.add('📈 Marché instable - Surveiller');
    }
    
    return suggestions.join(' | ');
  }
  
  static Prediction _createBasicPrediction(String message) {
    return Prediction.create(
      predictedMultiplier: 1.5,
      crashProbability: 0.5,
      riskLevel: 'INCONNU',
      confidence: 0.0,
      suggestion: message,
      method: 'basic',
      modelPredictions: {},
      volatilityIndex: 0.0,
      trendMomentum: 0.0,
    );
  }
}
