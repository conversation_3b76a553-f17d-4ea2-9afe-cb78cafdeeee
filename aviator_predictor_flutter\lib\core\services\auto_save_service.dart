import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import '../../data/models/game_result.dart';
import '../../data/models/prediction.dart';

class AutoSaveService {
  static const String _resultsBoxName = 'game_results_auto';
  static const String _predictionsBoxName = 'predictions_auto';
  static const String _backupFileName = 'aviator_backup.json';
  
  late Box<GameResult> _resultsBox;
  late Box<Prediction> _predictionsBox;
  Timer? _autoSaveTimer;
  Timer? _backupTimer;
  
  bool _isInitialized = false;
  
  // Singleton pattern
  static final AutoSaveService _instance = AutoSaveService._internal();
  factory AutoSaveService() => _instance;
  AutoSaveService._internal();

  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Ouvrir les boxes Hive
      _resultsBox = await Hive.openBox<GameResult>(_resultsBoxName);
      _predictionsBox = await Hive.openBox<Prediction>(_predictionsBoxName);
      
      // Démarrer les timers de sauvegarde automatique
      _startAutoSaveTimer();
      _startBackupTimer();
      
      _isInitialized = true;
      print('AutoSaveService initialisé avec succès');
    } catch (e) {
      print('Erreur lors de l\'initialisation d\'AutoSaveService: $e');
      rethrow;
    }
  }

  // Sauvegarde automatique d'un résultat de jeu
  Future<void> saveGameResult(GameResult result) async {
    if (!_isInitialized) await initialize();
    
    try {
      // Marquer comme sauvegardé automatiquement
      final autoSavedResult = result.copyWith(isAutoSaved: true);
      
      // Sauvegarder dans Hive
      await _resultsBox.put(result.id, autoSavedResult);
      
      // Nettoyer les anciens résultats si nécessaire
      await _cleanOldResults();
      
      print('Résultat sauvegardé automatiquement: ${result.multiplier}x');
    } catch (e) {
      print('Erreur lors de la sauvegarde automatique: $e');
    }
  }

  // Sauvegarde automatique d'une prédiction
  Future<void> savePrediction(Prediction prediction) async {
    if (!_isInitialized) await initialize();
    
    try {
      await _predictionsBox.put(prediction.id, prediction);
      print('Prédiction sauvegardée automatiquement: ${prediction.predictedMultiplier}x');
    } catch (e) {
      print('Erreur lors de la sauvegarde de prédiction: $e');
    }
  }

  // Récupérer tous les résultats sauvegardés
  List<GameResult> getAllResults() {
    if (!_isInitialized) return [];
    return _resultsBox.values.toList()..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  // Récupérer toutes les prédictions sauvegardées
  List<Prediction> getAllPredictions() {
    if (!_isInitialized) return [];
    return _predictionsBox.values.toList()..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  // Récupérer les résultats récents (dernières 24h)
  List<GameResult> getRecentResults() {
    final now = DateTime.now();
    final yesterday = now.subtract(const Duration(hours: 24));
    
    return getAllResults()
        .where((result) => result.timestamp.isAfter(yesterday))
        .toList();
  }

  // Récupérer les résultats par session de jeu
  List<GameResult> getResultsBySession(String sessionId) {
    return getAllResults()
        .where((result) => result.gameSession == sessionId)
        .toList();
  }

  // Calculer les statistiques de précision
  Map<String, double> calculateAccuracyStats() {
    final results = getAllResults()
        .where((r) => r.predictionAccuracy != null)
        .toList();
    
    if (results.isEmpty) {
      return {
        'averageAccuracy': 0.0,
        'bestAccuracy': 0.0,
        'worstAccuracy': 0.0,
        'totalPredictions': 0.0,
      };
    }
    
    final accuracies = results.map((r) => r.predictionAccuracy!).toList();
    
    return {
      'averageAccuracy': accuracies.reduce((a, b) => a + b) / accuracies.length,
      'bestAccuracy': accuracies.reduce((a, b) => a > b ? a : b),
      'worstAccuracy': accuracies.reduce((a, b) => a < b ? a : b),
      'totalPredictions': results.length.toDouble(),
    };
  }

  // Analyser les patterns de multiplicateurs
  Map<String, dynamic> analyzeMultiplierPatterns() {
    final results = getAllResults();
    if (results.isEmpty) return {};
    
    final multipliers = results.map((r) => r.multiplier).toList();
    
    // Calculer les probabilités par range
    final lowCount = multipliers.where((m) => m < 2.0).length;
    final mediumCount = multipliers.where((m) => m >= 2.0 && m < 5.0).length;
    final highCount = multipliers.where((m) => m >= 5.0 && m < 10.0).length;
    final extremeCount = multipliers.where((m) => m >= 10.0).length;
    
    final total = results.length;
    
    return {
      'totalGames': total,
      'probabilities': {
        'low': (lowCount / total) * 100,      // < 2.0x
        'medium': (mediumCount / total) * 100, // 2.0x - 5.0x
        'high': (highCount / total) * 100,     // 5.0x - 10.0x
        'extreme': (extremeCount / total) * 100, // >= 10.0x
      },
      'averageMultiplier': multipliers.reduce((a, b) => a + b) / multipliers.length,
      'maxMultiplier': multipliers.reduce((a, b) => a > b ? a : b),
      'minMultiplier': multipliers.reduce((a, b) => a < b ? a : b),
    };
  }

  // Prédire la probabilité d'un multiplicateur spécifique
  double predictMultiplierProbability(double targetMultiplier) {
    return GameResult.calculateMultiplierProbability(targetMultiplier, getAllResults());
  }

  // Prédire le temps de crash pour un multiplicateur
  int? predictCrashTimeForMultiplier(double targetMultiplier) {
    return GameResult.predictCrashTime(getAllResults(), targetMultiplier);
  }

  // Sauvegarde de backup en fichier JSON
  Future<void> createBackup() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupFile = File('${directory.path}/$_backupFileName');
      
      final backup = {
        'timestamp': DateTime.now().toIso8601String(),
        'version': '2.0.0',
        'results': getAllResults().map((r) => r.toJson()).toList(),
        'predictions': getAllPredictions().map((p) => p.toJson()).toList(),
        'stats': calculateAccuracyStats(),
        'patterns': analyzeMultiplierPatterns(),
      };
      
      await backupFile.writeAsString(jsonEncode(backup));
      print('Backup créé: ${backupFile.path}');
    } catch (e) {
      print('Erreur lors de la création du backup: $e');
    }
  }

  // Restaurer depuis un backup
  Future<void> restoreFromBackup() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupFile = File('${directory.path}/$_backupFileName');
      
      if (!await backupFile.exists()) {
        print('Aucun fichier de backup trouvé');
        return;
      }
      
      final backupContent = await backupFile.readAsString();
      final backup = jsonDecode(backupContent);
      
      // Restaurer les résultats
      final results = (backup['results'] as List)
          .map((json) => GameResult.fromJson(json))
          .toList();
      
      for (final result in results) {
        await _resultsBox.put(result.id, result);
      }
      
      // Restaurer les prédictions
      final predictions = (backup['predictions'] as List)
          .map((json) => Prediction.fromJson(json))
          .toList();
      
      for (final prediction in predictions) {
        await _predictionsBox.put(prediction.id, prediction);
      }
      
      print('Backup restauré avec succès: ${results.length} résultats, ${predictions.length} prédictions');
    } catch (e) {
      print('Erreur lors de la restauration du backup: $e');
    }
  }

  // Nettoyer les anciens résultats (garder seulement les 1000 derniers)
  Future<void> _cleanOldResults() async {
    final results = getAllResults();
    if (results.length > 1000) {
      final toDelete = results.skip(1000).toList();
      for (final result in toDelete) {
        await _resultsBox.delete(result.id);
      }
      print('${toDelete.length} anciens résultats supprimés');
    }
  }

  // Timer de sauvegarde automatique (toutes les 5 minutes)
  void _startAutoSaveTimer() {
    _autoSaveTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _compactDatabase();
    });
  }

  // Timer de backup (toutes les heures)
  void _startBackupTimer() {
    _backupTimer = Timer.periodic(const Duration(hours: 1), (timer) {
      createBackup();
    });
  }

  // Compacter la base de données
  Future<void> _compactDatabase() async {
    try {
      await _resultsBox.compact();
      await _predictionsBox.compact();
      print('Base de données compactée');
    } catch (e) {
      print('Erreur lors de la compaction: $e');
    }
  }

  // Nettoyer et fermer le service
  Future<void> dispose() async {
    _autoSaveTimer?.cancel();
    _backupTimer?.cancel();
    
    await _resultsBox.close();
    await _predictionsBox.close();
    
    _isInitialized = false;
    print('AutoSaveService fermé');
  }

  // Statistiques du service
  Map<String, dynamic> getServiceStats() {
    return {
      'isInitialized': _isInitialized,
      'totalResults': _resultsBox.length,
      'totalPredictions': _predictionsBox.length,
      'lastBackup': 'Automatique toutes les heures',
      'autoSaveInterval': '5 minutes',
      'maxStoredResults': 1000,
    };
  }
}
