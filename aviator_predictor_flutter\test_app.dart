import 'package:flutter_test/flutter_test.dart';
import 'package:aviator_predictor_flutter/main.dart';
import 'package:aviator_predictor_flutter/data/models/game_result.dart';
import 'package:aviator_predictor_flutter/core/services/crash_timing_service.dart';
import 'package:aviator_predictor_flutter/core/services/multiplier_probability_service.dart';

void main() {
  group('Aviator Predictor Tests', () {
    test('GameResult creation test', () {
      final result = GameResult.create(
        multiplier: 2.45,
        betAmount: 100.0,
        strategy: 'manual',
      );
      
      expect(result.multiplier, 2.45);
      expect(result.betAmount, 100.0);
      expect(result.strategy, 'manual');
      expect(result.isCrash, false); // 2.45 > 1.1, donc pas de crash
      expect(result.isAutoSaved, true);
    });

    test('Crash detection test', () {
      final crashResult = GameResult.create(multiplier: 1.05);
      final normalResult = GameResult.create(multiplier: 2.50);
      
      expect(crashResult.isCrash, true);
      expect(normalResult.isCrash, false);
    });

    test('Multiplier probability calculation', () {
      final history = [
        GameResult.create(multiplier: 1.5),
        GameResult.create(multiplier: 2.0),
        GameResult.create(multiplier: 2.5),
        GameResult.create(multiplier: 3.0),
        GameResult.create(multiplier: 1.8),
      ];
      
      final probability = MultiplierProbabilityService
          .calculateSpecificMultiplierProbability(2.0, history);
      
      expect(probability, greaterThan(0));
      expect(probability, lessThanOrEqualTo(100));
    });

    test('Crash timing prediction', () {
      final history = [
        GameResult.create(multiplier: 1.5, crashTimeSeconds: 10),
        GameResult.create(multiplier: 2.0, crashTimeSeconds: 15),
        GameResult.create(multiplier: 2.5, crashTimeSeconds: 20),
      ];
      
      final prediction = CrashTimingService.predictCrashTiming(history);
      
      expect(prediction.predictedCrashTime, greaterThan(0));
      expect(prediction.confidence, greaterThan(0));
      expect(prediction.confidence, lessThanOrEqualTo(100));
    });

    test('Multiplier analysis', () {
      final history = List.generate(50, (index) => 
        GameResult.create(multiplier: 1.0 + (index % 10) * 0.5)
      );
      
      final analysis = MultiplierProbabilityService
          .analyzeMultiplierProbabilities(history);
      
      expect(analysis.totalGames, 50);
      expect(analysis.rangeProbabilities.isNotEmpty, true);
    });

    test('Risk level calculation', () {
      final lowRisk = GameResult.create(multiplier: 1.5);
      final mediumRisk = GameResult.create(multiplier: 3.0);
      final highRisk = GameResult.create(multiplier: 7.0);
      final extremeRisk = GameResult.create(multiplier: 15.0);
      
      expect(lowRisk.riskLevel, 'LOW');
      expect(mediumRisk.riskLevel, 'MEDIUM');
      expect(highRisk.riskLevel, 'HIGH');
      expect(extremeRisk.riskLevel, 'EXTREME');
    });

    test('Prediction accuracy calculation', () {
      final result = GameResult.create(
        multiplier: 2.5,
        predictedMultiplier: 2.4,
      );
      
      expect(result.predictionAccuracy, isNotNull);
      expect(result.predictionAccuracy!, greaterThan(90)); // Très proche
    });
  });
}
