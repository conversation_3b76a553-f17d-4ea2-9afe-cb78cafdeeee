import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../core/theme/app_theme.dart';
import '../../data/models/game_result.dart';

class MultiplierChart extends StatefulWidget {
  final List<GameResult> results;
  final bool showPredictions;
  final List<double>? predictions;

  const MultiplierChart({
    super.key,
    required this.results,
    this.showPredictions = false,
    this.predictions,
  });

  @override
  State<MultiplierChart> createState() => _MultiplierChartState();
}

class _MultiplierChartState extends State<MultiplierChart> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _showCrashesOnly = false;
  bool _showTrendLine = true;
  int _visibleDataPoints = 50;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildChartControls(),
        const SizedBox(height: 16),
        Expanded(
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Transform.scale(
                scale: _animation.value,
                child: _buildChart(),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildChartControls() {
    return Row(
      children: [
        Expanded(
          child: Row(
            children: [
              Switch(
                value: _showCrashesOnly,
                onChanged: (value) {
                  setState(() {
                    _showCrashesOnly = value;
                  });
                },
                activeColor: AppTheme.primaryColor,
              ),
              const SizedBox(width: 8),
              const Text('Crashs uniquement'),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              Switch(
                value: _showTrendLine,
                onChanged: (value) {
                  setState(() {
                    _showTrendLine = value;
                  });
                },
                activeColor: AppTheme.secondaryColor,
              ),
              const SizedBox(width: 8),
              const Text('Ligne de tendance'),
            ],
          ),
        ),
        PopupMenuButton<int>(
          initialValue: _visibleDataPoints,
          onSelected: (value) {
            setState(() {
              _visibleDataPoints = value;
            });
          },
          itemBuilder: (context) => [
            const PopupMenuItem(value: 20, child: Text('20 points')),
            const PopupMenuItem(value: 50, child: Text('50 points')),
            const PopupMenuItem(value: 100, child: Text('100 points')),
            const PopupMenuItem(value: -1, child: Text('Tous')),
          ],
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              border: Border.all(color: AppTheme.primaryColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('${_visibleDataPoints == -1 ? 'Tous' : _visibleDataPoints}'),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildChart() {
    final filteredResults = _getFilteredResults();
    
    if (filteredResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.show_chart,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'Aucune donnée à afficher',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ),
      );
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          horizontalInterval: 1,
          verticalInterval: 5,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.1),
              strokeWidth: 1,
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.1),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 10,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < filteredResults.length) {
                  final result = filteredResults[value.toInt()];
                  return SideTitleWidget(
                    axisSide: meta.axisSide,
                    child: Text(
                      '${result.timestamp.hour.toString().padLeft(2, '0')}:${result.timestamp.minute.toString().padLeft(2, '0')}',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                        fontSize: 10,
                      ),
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: 2,
              reservedSize: 42,
              getTitlesWidget: (value, meta) {
                return SideTitleWidget(
                  axisSide: meta.axisSide,
                  child: Text(
                    '${value.toStringAsFixed(1)}x',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      fontSize: 10,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
          ),
        ),
        minX: 0,
        maxX: filteredResults.length.toDouble() - 1,
        minY: 0,
        maxY: _getMaxY(filteredResults),
        lineBarsData: [
          _buildMainLine(filteredResults),
          if (_showTrendLine) _buildTrendLine(filteredResults),
          if (widget.showPredictions && widget.predictions != null)
            _buildPredictionLine(filteredResults),
        ],
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            tooltipBgColor: Theme.of(context).colorScheme.surface,
            tooltipRoundedRadius: 8,
            getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
              return touchedBarSpots.map((barSpot) {
                final result = filteredResults[barSpot.x.toInt()];
                return LineTooltipItem(
                  '${result.multiplier.toStringAsFixed(2)}x\n',
                  TextStyle(
                    color: AppTheme.getMultiplierColor(result.multiplier),
                    fontWeight: FontWeight.bold,
                  ),
                  children: [
                    TextSpan(
                      text: '${result.timestamp.hour.toString().padLeft(2, '0')}:${result.timestamp.minute.toString().padLeft(2, '0')}',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        fontSize: 12,
                      ),
                    ),
                    if (result.betAmount != null)
                      TextSpan(
                        text: '\nMise: ${result.betAmount!.toStringAsFixed(0)} MGA',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                          fontSize: 12,
                        ),
                      ),
                    if (result.profit != null)
                      TextSpan(
                        text: '\nProfit: ${result.profit!.toStringAsFixed(0)} MGA',
                        style: TextStyle(
                          color: result.profit! > 0 ? AppTheme.successColor : AppTheme.errorColor,
                          fontSize: 12,
                        ),
                      ),
                  ],
                );
              }).toList();
            },
          ),
        ),
      ),
    );
  }

  List<GameResult> _getFilteredResults() {
    var results = widget.results.toList();
    
    if (_showCrashesOnly) {
      results = results.where((r) => r.isCrash).toList();
    }
    
    if (_visibleDataPoints > 0 && results.length > _visibleDataPoints) {
      results = results.take(_visibleDataPoints).toList();
    }
    
    return results.reversed.toList(); // Show most recent first
  }

  double _getMaxY(List<GameResult> results) {
    if (results.isEmpty) return 10;
    final maxMultiplier = results.map((r) => r.multiplier).reduce((a, b) => a > b ? a : b);
    return (maxMultiplier * 1.2).clamp(5.0, 50.0);
  }

  LineChartBarData _buildMainLine(List<GameResult> results) {
    return LineChartBarData(
      spots: results.asMap().entries.map((entry) {
        return FlSpot(entry.key.toDouble(), entry.value.multiplier);
      }).toList(),
      isCurved: true,
      gradient: LinearGradient(
        colors: [
          AppTheme.primaryColor.withOpacity(0.8),
          AppTheme.secondaryColor.withOpacity(0.8),
        ],
      ),
      barWidth: 3,
      isStrokeCapRound: true,
      dotData: FlDotData(
        show: true,
        getDotPainter: (spot, percent, barData, index) {
          final result = results[index];
          return FlDotCirclePainter(
            radius: result.isCrash ? 6 : 4,
            color: AppTheme.getMultiplierColor(result.multiplier),
            strokeWidth: 2,
            strokeColor: Colors.white,
          );
        },
      ),
      belowBarData: BarAreaData(
        show: true,
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor.withOpacity(0.3),
            AppTheme.primaryColor.withOpacity(0.1),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
    );
  }

  LineChartBarData _buildTrendLine(List<GameResult> results) {
    if (results.length < 2) {
      return LineChartBarData(spots: []);
    }

    // Calculate linear regression
    final n = results.length;
    double sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;
    
    for (int i = 0; i < n; i++) {
      sumX += i;
      sumY += results[i].multiplier;
      sumXY += i * results[i].multiplier;
      sumX2 += i * i;
    }
    
    final slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    final intercept = (sumY - slope * sumX) / n;
    
    return LineChartBarData(
      spots: [
        FlSpot(0, intercept),
        FlSpot(n.toDouble() - 1, slope * (n - 1) + intercept),
      ],
      isCurved: false,
      color: AppTheme.warningColor,
      barWidth: 2,
      isStrokeCapRound: true,
      dotData: const FlDotData(show: false),
      dashArray: [5, 5],
    );
  }

  LineChartBarData _buildPredictionLine(List<GameResult> results) {
    if (widget.predictions == null || widget.predictions!.isEmpty) {
      return LineChartBarData(spots: []);
    }

    final startX = results.length.toDouble();
    final predictions = widget.predictions!;
    
    return LineChartBarData(
      spots: predictions.asMap().entries.map((entry) {
        return FlSpot(startX + entry.key, entry.value);
      }).toList(),
      isCurved: true,
      color: AppTheme.secondaryColor,
      barWidth: 2,
      isStrokeCapRound: true,
      dotData: FlDotData(
        show: true,
        getDotPainter: (spot, percent, barData, index) {
          return FlDotCirclePainter(
            radius: 4,
            color: AppTheme.secondaryColor,
            strokeWidth: 2,
            strokeColor: Colors.white,
          );
        },
      ),
      dashArray: [3, 3],
    );
  }
}
