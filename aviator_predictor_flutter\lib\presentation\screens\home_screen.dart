import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../core/theme/app_theme.dart';
import '../../data/models/game_result.dart';
import '../../data/models/prediction.dart';
import '../providers/game_provider.dart';
import '../widgets/prediction_card.dart';
import '../widgets/stats_card.dart';
import '../widgets/multiplier_chart.dart';
import '../widgets/add_result_dialog.dart';
import '../widgets/control_panel.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameProvider);
    final isDesktop = MediaQuery.of(context).size.width > 1200;
    final isTablet = MediaQuery.of(context).size.width > 600;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.flight_takeoff,
                color: Colors.black,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text('Aviator Predictor Pro'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add_circle_outline),
            onPressed: () => _showAddResultDialog(context),
            tooltip: 'Ajouter un résultat',
          ),
          IconButton(
            icon: const Icon(Icons.analytics_outlined),
            onPressed: () => _showPredictionDialog(context),
            tooltip: 'Nouvelle prédiction',
          ),
          IconButton(
            icon: Icon(
              Theme.of(context).brightness == Brightness.dark
                  ? Icons.light_mode
                  : Icons.dark_mode,
            ),
            onPressed: () => ref.read(themeProvider.notifier).toggleTheme(),
            tooltip: 'Changer le thème',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: _buildBody(context, gameState, isDesktop, isTablet),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddResultDialog(context),
        icon: const Icon(Icons.add),
        label: const Text('Ajouter'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.black,
      ),
    );
  }

  Widget _buildBody(BuildContext context, GameState gameState, bool isDesktop, bool isTablet) {
    if (isDesktop) {
      return _buildDesktopLayout(context, gameState);
    } else if (isTablet) {
      return _buildTabletLayout(context, gameState);
    } else {
      return _buildMobileLayout(context, gameState);
    }
  }

  Widget _buildDesktopLayout(BuildContext context, GameState gameState) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left panel - Stats and controls
          Expanded(
            flex: 1,
            child: Column(
              children: [
                const ControlPanel(),
                const SizedBox(height: 24),
                _buildStatsSection(gameState),
                const SizedBox(height: 24),
                _buildRecentResults(gameState),
              ],
            ),
          ),
          const SizedBox(width: 24),
          // Center panel - Chart
          Expanded(
            flex: 2,
            child: Column(
              children: [
                _buildChartSection(gameState),
                const SizedBox(height: 24),
                _buildPredictionSection(gameState),
              ],
            ),
          ),
          const SizedBox(width: 24),
          // Right panel - Analysis
          Expanded(
            flex: 1,
            child: Column(
              children: [
                _buildAnalysisSection(gameState),
                const SizedBox(height: 24),
                _buildAlertsSection(gameState),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabletLayout(BuildContext context, GameState gameState) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Top row - Stats
          _buildStatsSection(gameState),
          const SizedBox(height: 16),
          // Chart section
          Expanded(
            flex: 2,
            child: _buildChartSection(gameState),
          ),
          const SizedBox(height: 16),
          // Bottom row - Prediction and Analysis
          Expanded(
            flex: 1,
            child: Row(
              children: [
                Expanded(child: _buildPredictionSection(gameState)),
                const SizedBox(width: 16),
                Expanded(child: _buildAnalysisSection(gameState)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context, GameState gameState) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          _buildStatsSection(gameState),
          const SizedBox(height: 16),
          _buildChartSection(gameState),
          const SizedBox(height: 16),
          _buildPredictionSection(gameState),
          const SizedBox(height: 16),
          _buildAnalysisSection(gameState),
          const SizedBox(height: 16),
          _buildQuickActions(context),
          const SizedBox(height: 100), // Space for FAB
        ],
      ),
    );
  }

  Widget _buildStatsSection(GameState gameState) {
    return StatsCard(
      totalGames: gameState.results.length,
      averageMultiplier: gameState.averageMultiplier,
      crashRate: gameState.crashRate,
      totalProfit: gameState.totalProfit,
      winRate: gameState.winRate,
      currentStreak: gameState.currentStreak,
    );
  }

  Widget _buildChartSection(GameState gameState) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.show_chart, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Historique des Multiplicateurs',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: MultiplierChart(results: gameState.results),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPredictionSection(GameState gameState) {
    return PredictionCard(
      prediction: gameState.lastPrediction,
      onPredict: () => _makePrediction(context),
    );
  }

  Widget _buildAnalysisSection(GameState gameState) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: AppTheme.secondaryColor),
                const SizedBox(width: 8),
                Text(
                  'Analyse Avancée',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildAnalysisMetric(
              'Volatilité',
              '${(gameState.volatilityIndex * 100).toStringAsFixed(1)}%',
              AppTheme.getCrashProbabilityColor(gameState.volatilityIndex),
            ),
            const SizedBox(height: 8),
            _buildAnalysisMetric(
              'Momentum',
              gameState.trendMomentum > 0 ? '+${gameState.trendMomentum.toStringAsFixed(2)}' : gameState.trendMomentum.toStringAsFixed(2),
              gameState.trendMomentum > 0 ? AppTheme.successColor : AppTheme.errorColor,
            ),
            const SizedBox(height: 8),
            _buildAnalysisMetric(
              'Score de Risque',
              '${gameState.riskScore.toStringAsFixed(0)}/100',
              AppTheme.getCrashProbabilityColor(gameState.riskScore / 100),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisMetric(String label, String value, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: Theme.of(context).textTheme.bodyMedium),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: color),
          ),
          child: Text(
            value,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Actions Rapides',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _makePrediction(context),
                    icon: const Icon(Icons.psychology),
                    label: const Text('Prédire'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showAnalysisDialog(context),
                    icon: const Icon(Icons.analytics),
                    label: const Text('Analyser'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentResults(GameState gameState) {
    final recentResults = gameState.results.take(5).toList();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Résultats Récents',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ...recentResults.map((result) => _buildResultItem(result)),
          ],
        ),
      ),
    );
  }

  Widget _buildResultItem(GameResult result) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '${result.timestamp.hour.toString().padLeft(2, '0')}:${result.timestamp.minute.toString().padLeft(2, '0')}',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: AppTheme.getMultiplierColor(result.multiplier).withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${result.multiplier.toStringAsFixed(2)}x',
              style: TextStyle(
                color: AppTheme.getMultiplierColor(result.multiplier),
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertsSection(GameState gameState) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.warning_amber, color: AppTheme.warningColor),
                const SizedBox(width: 8),
                Text(
                  'Alertes',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (gameState.alerts.isEmpty)
              Text(
                'Aucune alerte active',
                style: Theme.of(context).textTheme.bodyMedium,
              )
            else
              ...gameState.alerts.map((alert) => _buildAlertItem(alert)),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertItem(String alert) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          const Icon(Icons.circle, size: 8, color: AppTheme.warningColor),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              alert,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  void _showAddResultDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const AddResultDialog(),
    );
  }

  void _showPredictionDialog(BuildContext context) {
    _makePrediction(context);
  }

  void _showAnalysisDialog(BuildContext context) {
    // TODO: Implement analysis dialog
  }

  void _makePrediction(BuildContext context) {
    ref.read(gameProvider.notifier).makePrediction();
  }
}
