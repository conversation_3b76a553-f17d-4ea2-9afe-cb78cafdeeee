import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../presentation/screens/home_screen.dart';
import '../../presentation/screens/analytics_screen.dart';
import '../../presentation/screens/settings_screen.dart';
import '../../presentation/screens/prediction_lab_screen.dart';

final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/',
    routes: [
      GoRoute(
        path: '/',
        name: 'home',
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: '/analytics',
        name: 'analytics',
        builder: (context, state) => const AnalyticsScreen(),
      ),
      GoRoute(
        path: '/prediction-lab',
        name: 'prediction-lab',
        builder: (context, state) => const PredictionLabScreen(),
      ),
      GoRoute(
        path: '/settings',
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
      ),
    ],
  );
});

// Navigation helper
class AppNavigation {
  static void goToHome(BuildContext context) {
    context.go('/');
  }
  
  static void goToAnalytics(BuildContext context) {
    context.go('/analytics');
  }
  
  static void goToPredictionLab(BuildContext context) {
    context.go('/prediction-lab');
  }
  
  static void goToSettings(BuildContext context) {
    context.go('/settings');
  }
}
