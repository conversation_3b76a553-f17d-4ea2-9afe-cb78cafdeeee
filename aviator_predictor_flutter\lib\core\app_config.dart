import 'dart:io';
import 'package:flutter/foundation.dart';

class AppConfig {
  static const String appName = 'Aviator Predictor Pro';
  static const String version = '2.0.0';
  static const String buildNumber = '1';
  
  // Platform detection
  static bool get isDesktop => !kIsWeb && (Platform.isWindows || Platform.isLinux || Platform.isMacOS);
  static bool get isMobile => !kIsWeb && (Platform.isAndroid || Platform.isIOS);
  static bool get isWeb => kIsWeb;
  
  // Platform-specific configurations
  static Map<String, dynamic> get platformConfig {
    if (isDesktop) {
      return {
        'windowSize': {'width': 1400, 'height': 900},
        'minWindowSize': {'width': 1200, 'height': 800},
        'enableWindowControls': true,
        'enableMenuBar': true,
        'chartAnimations': true,
        'maxDataPoints': 1000,
        'autoSave': true,
        'exportFormats': ['json', 'csv', 'pdf'],
      };
    } else if (isMobile) {
      return {
        'enableHapticFeedback': true,
        'enableNotifications': true,
        'chartAnimations': true,
        'maxDataPoints': 500,
        'autoSave': true,
        'exportFormats': ['json', 'csv'],
        'orientationSupport': ['portrait', 'landscape'],
      };
    } else if (isWeb) {
      return {
        'enablePWA': true,
        'enableOfflineMode': true,
        'chartAnimations': true,
        'maxDataPoints': 800,
        'autoSave': false,
        'exportFormats': ['json', 'csv'],
        'enableSharing': true,
      };
    }
    return {};
  }
  
  // Feature flags
  static bool get enableAdvancedAlgorithms => true;
  static bool get enableRealTimeUpdates => true;
  static bool get enableCloudSync => false; // Future feature
  static bool get enableMultiLanguage => false; // Future feature
  static bool get enableDarkMode => true;
  static bool get enableExport => true;
  static bool get enableImport => true;
  
  // Performance settings
  static int get maxHistoryItems => platformConfig['maxDataPoints'] ?? 500;
  static bool get enableAnimations => platformConfig['chartAnimations'] ?? true;
  static Duration get animationDuration => const Duration(milliseconds: 300);
  static Duration get chartUpdateInterval => const Duration(milliseconds: 100);
  
  // Data settings
  static double get crashThreshold => 1.1;
  static int get minDataForPrediction => 10;
  static int get minDataForAdvancedPrediction => 20;
  static int get defaultWindowSize => 15;
  static double get defaultConfidenceThreshold => 70.0;
  
  // UI settings
  static double get cardBorderRadius => 16.0;
  static double get buttonBorderRadius => 12.0;
  static double get defaultPadding => 16.0;
  static double get compactPadding => 8.0;
  
  // Chart settings
  static int get defaultChartPoints => 50;
  static bool get enableChartZoom => isDesktop;
  static bool get enableChartPan => true;
  static bool get showChartGrid => true;
  static bool get showChartTooltips => true;
  
  // Prediction settings
  static List<String> get availableMethods => [
    'hybrid',
    'conservative',
    'aggressive',
    'adaptive',
    'lstm',
    'ensemble',
    'reinforcement',
    'genetic',
    'quantum',
  ];
  
  static Map<String, String> get methodDescriptions => {
    'hybrid': 'Combinaison équilibrée de plusieurs algorithmes',
    'conservative': 'Approche prudente avec prédictions basses',
    'aggressive': 'Approche audacieuse avec prédictions élevées',
    'adaptive': 'Adaptation automatique selon la volatilité',
    'lstm': 'Réseau de neurones à mémoire long-terme',
    'ensemble': 'Combinaison de multiples modèles ML',
    'reinforcement': 'Apprentissage par renforcement',
    'genetic': 'Algorithme génétique évolutionnaire',
    'quantum': 'Simulation quantique probabiliste',
  };
  
  // Storage settings
  static String get resultsBoxName => 'game_results';
  static String get predictionsBoxName => 'predictions';
  static String get settingsBoxName => 'app_settings';
  static String get cacheBoxName => 'app_cache';
  
  // Network settings (for future cloud features)
  static String get apiBaseUrl => 'https://api.aviatorpredictor.com';
  static Duration get networkTimeout => const Duration(seconds: 30);
  static int get maxRetries => 3;
  
  // Analytics settings (for future analytics)
  static bool get enableAnalytics => false;
  static bool get enableCrashReporting => false;
  static bool get enablePerformanceMonitoring => false;
  
  // Security settings
  static bool get enableDataEncryption => false; // Future feature
  static bool get enableBiometricAuth => false; // Future feature
  
  // Accessibility settings
  static bool get enableHighContrast => false;
  static bool get enableLargeText => false;
  static bool get enableScreenReader => false;
  
  // Development settings
  static bool get isDebugMode => kDebugMode;
  static bool get enableLogging => kDebugMode;
  static bool get enablePerformanceProfiling => kDebugMode;
  
  // Platform-specific methods
  static String getPlatformName() {
    if (isDesktop) {
      if (Platform.isWindows) return 'Windows';
      if (Platform.isMacOS) return 'macOS';
      if (Platform.isLinux) return 'Linux';
    } else if (isMobile) {
      if (Platform.isAndroid) return 'Android';
      if (Platform.isIOS) return 'iOS';
    } else if (isWeb) {
      return 'Web';
    }
    return 'Unknown';
  }
  
  static bool supportsFeature(String feature) {
    switch (feature) {
      case 'window_controls':
        return isDesktop;
      case 'haptic_feedback':
        return isMobile;
      case 'file_system':
        return !isWeb;
      case 'notifications':
        return isMobile || isWeb;
      case 'offline_mode':
        return !isWeb || platformConfig['enableOfflineMode'] == true;
      case 'export_pdf':
        return isDesktop;
      case 'sharing':
        return isMobile || isWeb;
      default:
        return false;
    }
  }
  
  static Map<String, dynamic> getOptimalSettings() {
    return {
      'chartPoints': isDesktop ? 100 : (isMobile ? 50 : 75),
      'animationDuration': isDesktop ? 300 : (isMobile ? 200 : 250),
      'updateInterval': isDesktop ? 100 : (isMobile ? 200 : 150),
      'maxHistory': isDesktop ? 1000 : (isMobile ? 500 : 800),
      'enableAnimations': enableAnimations,
      'enableHaptics': isMobile,
      'enableSounds': isMobile,
    };
  }
}
