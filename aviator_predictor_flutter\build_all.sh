#!/bin/bash

echo "========================================"
echo "   AVIATOR PREDICTOR PRO - BUILD SCRIPT"
echo "========================================"
echo

# Vérifier que Flutter est installé
if ! command -v flutter &> /dev/null; then
    echo "ERREUR: Flutter n'est pas installé ou n'est pas dans le PATH"
    echo "Veuillez installer Flutter: https://flutter.dev/docs/get-started/install"
    exit 1
fi

echo "[1/6] Vérification de l'environnement Flutter..."
flutter doctor --android-licenses > /dev/null 2>&1

echo "[2/6] Nettoyage des builds précédents..."
flutter clean
flutter pub get

# Détecter la plateforme
if [[ "$OSTYPE" == "darwin"* ]]; then
    PLATFORM="macos"
    echo "[3/6] Compilation pour macOS..."
    flutter build macos --release
else
    PLATFORM="linux"
    echo "[3/6] Compilation pour Linux..."
    flutter build linux --release
fi

if [ $? -ne 0 ]; then
    echo "ERREUR: Échec de la compilation $PLATFORM"
    exit 1
fi

echo "[4/6] Compilation pour Web..."
flutter build web --release --web-renderer html
if [ $? -ne 0 ]; then
    echo "ERREUR: Échec de la compilation Web"
    exit 1
fi

echo "[5/6] Compilation pour Android APK..."
flutter build apk --release --split-per-abi
if [ $? -ne 0 ]; then
    echo "ERREUR: Échec de la compilation Android"
    exit 1
fi

echo "[6/6] Création du package de distribution..."

# Créer le dossier de distribution
rm -rf "dist"
mkdir -p "dist/$PLATFORM"
mkdir -p "dist/web"
mkdir -p "dist/android"

# Copier les builds
echo "Copie des fichiers $PLATFORM..."
if [[ "$PLATFORM" == "macos" ]]; then
    cp -r "build/macos/Build/Products/Release/"* "dist/macos/"
else
    cp -r "build/linux/x64/release/bundle/"* "dist/linux/"
fi

echo "Copie des fichiers Web..."
cp -r "build/web/"* "dist/web/"

echo "Copie des fichiers Android..."
cp build/app/outputs/flutter-apk/*.apk "dist/android/"

# Créer un fichier README
echo "Création du fichier README..."
cat > "dist/README.txt" << EOF
AVIATOR PREDICTOR PRO - DISTRIBUTION
====================================

Ce package contient les versions compilées de l'application Aviator Predictor Pro
pour différentes plateformes.

CONTENU:
--------

1. $(echo $PLATFORM | tr '[:lower:]' '[:upper:]') (dist/$PLATFORM/)
   - Exécutable: aviator_predictor_flutter
   - Fichiers de support: lib/, data/, etc.
   - Configuration requise: $(if [[ "$PLATFORM" == "macos" ]]; then echo "macOS 10.14 ou plus récent"; else echo "Linux Ubuntu 18.04 ou plus récent"; fi)

2. WEB (dist/web/)
   - Fichiers web: index.html, main.dart.js, etc.
   - Déployez ces fichiers sur un serveur web
   - Compatible avec tous les navigateurs modernes

3. ANDROID (dist/android/)
   - Fichiers APK pour différentes architectures
   - app-arm64-v8a-release.apk (recommandé pour la plupart des appareils)
   - app-armeabi-v7a-release.apk (appareils plus anciens)
   - app-x86_64-release.apk (émulateurs)

INSTALLATION:
-------------

$(echo $PLATFORM | tr '[:lower:]' '[:upper:]'):
1. Extraire le contenu de dist/$PLATFORM/ dans un dossier
2. Exécuter aviator_predictor_flutter
$(if [[ "$PLATFORM" == "linux" ]]; then echo "3. Vous pourriez avoir besoin d'installer: sudo apt-get install libgtk-3-0 libblkid1 liblzma5"; fi)

WEB:
1. Copier le contenu de dist/web/ sur votre serveur web
2. Accéder à index.html via votre navigateur

ANDROID:
1. Transférer le fichier APK approprié sur votre appareil
2. Activer "Sources inconnues" dans les paramètres
3. Installer l'APK

FONCTIONNALITÉS:
----------------

✅ Prédictions avancées avec IA (LSTM, Ensemble, RL, Génétique, Quantique)
✅ Sauvegarde automatique de l'historique
✅ Prédiction du timing de crash
✅ Analyse des probabilités de multiplicateurs
✅ Interface moderne Material Design 3
✅ Graphiques interactifs en temps réel
✅ Mode sombre/clair
✅ Responsive design (desktop, tablet, mobile)
✅ Export/Import des données
✅ Alertes intelligentes
✅ Statistiques avancées

SUPPORT:
--------

Pour toute question ou problème, veuillez consulter la documentation
ou contacter le support technique.

Version: 2.0.0
Date de build: $(date)

EOF

# Créer un script de lancement
echo "Création du script de lancement..."
if [[ "$PLATFORM" == "macos" ]]; then
    cat > "dist/macos/Lancer_Aviator_Predictor.command" << EOF
#!/bin/bash
cd "\$(dirname "\$0")"
echo "Démarrage d'Aviator Predictor Pro..."
./aviator_predictor_flutter.app/Contents/MacOS/aviator_predictor_flutter
EOF
    chmod +x "dist/macos/Lancer_Aviator_Predictor.command"
else
    cat > "dist/linux/Lancer_Aviator_Predictor.sh" << EOF
#!/bin/bash
cd "\$(dirname "\$0")"
echo "Démarrage d'Aviator Predictor Pro..."
./aviator_predictor_flutter
EOF
    chmod +x "dist/linux/Lancer_Aviator_Predictor.sh"
fi

# Créer un fichier de version
cat > "dist/version.json" << EOF
{
  "name": "Aviator Predictor Pro",
  "version": "2.0.0",
  "build_date": "$(date)",
  "platforms": [
    "$(echo $PLATFORM | sed 's/.*/\u&/') x64",
    "Web (HTML)",
    "Android (ARM64, ARMv7, x86_64)"
  ],
  "features": [
    "Advanced AI Predictions",
    "Automatic History Saving",
    "Crash Timing Prediction",
    "Multiplier Probability Analysis",
    "Interactive Charts",
    "Material Design 3",
    "Dark/Light Theme",
    "Responsive Design",
    "Data Export/Import",
    "Smart Alerts"
  ]
}
EOF

echo
echo "========================================"
echo "          BUILD TERMINÉ AVEC SUCCÈS!"
echo "========================================"
echo
echo "Les applications ont été compilées et sont disponibles dans le dossier 'dist':"
echo
echo "📁 dist/"
echo "  ├── 🖥️  $PLATFORM/          (Application $PLATFORM)"
echo "  ├── 🌐 web/              (Application Web)"
echo "  ├── 📱 android/          (Applications Android APK)"
echo "  ├── 📄 README.txt        (Instructions d'installation)"
echo "  └── 📋 version.json      (Informations de version)"
echo
echo "TAILLES APPROXIMATIVES:"
if [[ "$PLATFORM" == "macos" ]]; then
    du -sh "dist/macos" 2>/dev/null | cut -f1 | xargs echo "macOS:"
else
    du -sh "dist/linux" 2>/dev/null | cut -f1 | xargs echo "Linux:"
fi
echo "Web: ~10-15 MB"
ls -lh "dist/android"/*.apk 2>/dev/null | awk '{print "Android:", $5, $9}' | head -3
echo
echo "Pour tester:"
if [[ "$PLATFORM" == "macos" ]]; then
    echo "- macOS: Exécuter dist/macos/aviator_predictor_flutter.app"
else
    echo "- Linux: Exécuter dist/linux/aviator_predictor_flutter"
fi
echo "- Web: Ouvrir dist/web/index.html dans un navigateur"
echo "- Android: Installer l'APK approprié sur votre appareil"
echo
echo "✅ Toutes les plateformes ont été compilées avec succès!"
echo
